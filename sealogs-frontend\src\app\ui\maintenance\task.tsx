'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import { ListBox, ListBoxItem, Popover } from 'react-aria-components'
import {
    GET_CREW_BY_IDS,
    GET_ENGINE_IDS_BY_VESSEL,
    GET_ENGINES,
    GET_FILES,
    GET_MAINTENANCE_CHECK,
    GET_MAINTENANCE_CHECK_SUBTASK,
    GET_RECURRING_TASK,
    GetTaskRecords,
    GetMaintenanceCategories,
    GET_INVENTORY_LIST,
    VESSEL_STATUS,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_COMPONENT_MAINTENANCE_CHECK,
    CREATE_SEALOGS_FILE_LINKS,
    UPDATE_COMPONENT_MAINTENANCE_SIGNATURE,
    DELETE_COMPONENT_MAINTENANCE_CHECK,
    UPDATE_COMPONENT_MAINTENANCE_SCHEDU<PERSON>,
    CREATE_COMPONENT_MAINTENANCE_SCHEDULE,
    CREATE_COMPONENT_MAINTENANCE_SUBTASK,
    UPDATE_COMPONENT_MAINTENANCE_SUBTASK,
    CREATE_MAINTENANCE_CHECK_SUBTASK,
    UPDATE_COMPONENT_MAINTENANCE_CHECK_SUBTASK,
    CreateMissionTimeline,
    UpdateMissionTimeline,
    CreateEngine_Usage,
    UpdateEngine_Usage,
    CREATE_MAINTENANCE_CATEGORY,
    CREATE_VESSELSTATUS,
    CREATE_R2FILE,
} from '@/app/lib/graphQL/mutation'
import {
    ChatBubbleBottomCenterTextIcon,
    XCircleIcon,
} from '@heroicons/react/24/outline'
import Select, { StylesConfig } from 'react-select'
import FileUpload from '@/components/file-upload'
import SignatureCanvas from 'react-signature-canvas'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import dayjs from 'dayjs'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { useQueryState } from 'nuqs'
import Link from 'next/link'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Button } from '@/components/ui/button'
import {
    Pencil,
    Trash,
    Plus,
    Check,
    X,
    AlertCircle,
    ArrowLeft,
    CheckCircle2,
} from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'
import {
    getInventoryList,
    getVesselList,
    getMaintenanceCheckByID,
    getSeaLogsMembersList,
    getMaintenanceCheckSubTaskByID,
    isOverDueTask,
} from '@/app/lib/actions'
import { create, isEmpty, set } from 'lodash'
import FileItem from '@/components/file-item'
import { useToast } from '@/hooks/use-toast'
// DateField replaced with DatePicker
import { formatDate } from '@/app/helpers/dateHelper'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { scheduler } from 'timers/promises'
import UploadCloudFlare from '../logbook/components/upload-cf'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ActionFooter } from '@/components/ui/action-footer'
import { Combobox } from '@/components/ui/comboBox'
import DatePicker from '@/components/DateRange'
import {
    AlertDialogBody,
    Badge,
    H1,
    H3,
    Label,
    P,
    Separator,
} from '@/components/ui'
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { useMediaQuery } from '@reactuses/core'
import { ScrollArea } from '@/components/ui/scroll-area'
import Editor from '../editor'
import { Progress } from '@/components/ui/progress'
import TableWrapper from '@/components/ui/table-wrapper'
import PopoverWrapper from '@/components/ui/popover-wrapper'

// Define interfaces for component props and state
interface TaskProps {
    taskId: number
    redirectTo: string
    inSidebar?: boolean
    onSidebarClose?: () => void
    vesselID?: number
}

export default function Task({
    taskId,
    redirectTo,
    inSidebar = false,
    onSidebarClose,
    vesselID = 0,
}: TaskProps) {
    const router = useRouter()
    const searchParams = useSearchParams()
    const pathname = usePathname()
    const { toast, dismiss } = useToast()
    const [vessels, setVessels] = useState<any>()
    const [currentVessel, setCurrentVessel] = useState<any>()
    const [crewMembers, setCrewMembers] = useState<any>()
    const [inventories, setInventories] = useState<any>()
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()
    const [signature, setSignature] = useState<any>()
    const [fileLinks, setFileLinks] = useState<any>([])
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const [currentTask, setCurrentTask] = useState<any>()
    const [isCompleted, setIsCompleted] = useState<boolean>(false)
    const [expiryDate, setExpiryDate] = useState<any>('')
    const [scheduleCompletedDate, setScheduleCompletedDate] = useState<any>('')
    const [authorID, setAuthorID] = useState<any>(0)
    const [completionDate, setCompletionDate] = useState<any>('')
    const [startDate, setStartDate] = useState<any>(false)
    const [openSubTaskDialog, setOpenSubTaskDialog] = useState(false)
    const [displayRecurringTasks, setDisplayRecurringTasks] = useState(false)
    const [recurringTasks, setRecurringTasks] = useState<any>(false)
    const [subTasks, setSubTask] = useState<any>([])
    const [currentSubTaskCheckID, setCurrentSubTaskCheckID] = useState<any>()
    const [currentSubTask, setCurrentSubTask] = useState<any>()
    const [displayAddFindings, setDisplayAddFindings] = useState(false)
    const [linkSelectedOption, setLinkSelectedOption] = useState<any>([])
    const [isLastCompletedDateChanged, setIsLastCompletedDateChanged] =
        useState(false)
    const [displayUpdateSubTask, setDisplayUpdateSubTask] = useState(false)
    const [alertSubTaskStatus, setAlertSubTaskStatus] = useState(false)
    const [displayUpdateFindings, setDisplayUpdateFindings] = useState(false)
    const [displayWarnings, setDisplayWarnings] = useState(false)
    const [costsDifference, setCostsDifference] = useState<any>(0)
    const [openDeleteTaskDialog, setOpenDeleteTaskDialog] = useState(false)
    const [openRecordsDialog, setOpenRecordsDialog] = useState(false)
    const [commentTime, setCommentTime] = useState<any>()
    const [openDeleteRecordDialog, setOpenDeleteRecordDialog] = useState(false)
    const [deleteRecordID, setDeleteRecordID] = useState<any>(0)
    const [commentData, setCommentData] = useState<any>(false)
    const [members, setMembers] = useState<any>()
    const [completedRecurringTasks, setCompletedRecurringTasks] = useState<any>(
        [],
    )
    const [crewInfo, setCrewInfo] = useState<any>()
    const [taskTab, setTaskTab] = useState('task')
    const [allInventories, setAllInventories] = useState<any>([])
    const [inventoryDefaultValue, setInventoryDefaultValue] =
        useState<any>(null)
    const [content, setContent] = useState<any>('')
    const [reviewContent, setReviewContent] = useState<any>('')
    const [subtaskContent, setSubtaskContent] = useState<any>('')
    const [subtaskInventoryValue, setSubtaskInventoryValue] = useState<any>(0)
    const [taskRecords, setTaskRecords] = useState<any[]>([])
    const [engineList, setEngineList] = useState<any>([])
    const [displayCheckEngineCheck, setDisplayCheckEngineCheck] = useState<any>(
        [],
    )
    const [engineHours, setEngineHours] = useState<any>([])
    const [categoryList, setCategoryList] = useState<any>([])
    const [createCategoryDialog, setCreateCategoryDialog] = useState(false)
    // Use nuqs to manage the tab state through URL query parameters
    const [activeTab, setActiveTab] = useQueryState('taskTab', {
        defaultValue: 'Details',
    })
    const [loadedInventory, setLoadedInventory] = useState(0)
    const [currentMaintenanceCheck, setCurrentMaintenanceCheck] =
        useState<any>(false)
    const isDesktop = useMediaQuery('(min-width: 768px)')

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const [delete_task, setDelete_task] = useState<any>(false)
    const [complete_task, setComplete_task] = useState<any>(false)
    const [edit_recurring_task, setEdit_recurring_task] = useState<any>(false)
    const [scheduleEvery, setScheduleEvery] = useState<any>(0)
    const [saveDisabled, setSaveDisabled] = useState(false)
    const [vesselStatus, setVesselStatus] = useState<any>(false)
    const [displayEditStatus, setDisplayEditStatus] = useState(false)
    const [attachments, setAttachments] = useState<any>([])
    const [subTaskAttachments, setSubTaskAttachments] = useState<any>([])

    const vesselStatuses: any = [
        { label: 'On Voyage', value: 'OnVoyage' },
        { label: 'Available For Voyage', value: 'AvailableForVoyage' },
        { label: 'Out Of Service', value: 'OutOfService' },
    ]
    const vesselStatusReason: any = [
        { label: 'Crew Unavailable', value: 'CrewUnavailable' },
        { label: 'Skipper/Master Unavailable', value: 'MasterUnavailable' },
        { label: 'Planned Maintenance', value: 'PlannedMaintenance' },
        { label: 'Breakdown', value: 'Breakdown' },
        { label: 'Other', value: 'Other' },
    ]

    const [getVesselStatus] = useLazyQuery(VESSEL_STATUS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readVesselStatuss.nodes
            if (data) {
                setVesselStatus(data[0])
            }
            if (data.length === 0) {
                createVesselStatus({
                    variables: {
                        input: {
                            date: dayjs().format('YYYY-MM-DD'),
                            vesselID: currentTask?.basicComponentID,
                        },
                    },
                })
            }
        },
        onError: (error) => {
            console.error('Error getting vessel status', error)
        },
    })

    useEffect(() => {
        if (inSidebar) {
            if (vessels && vessels.length > 0) {
                const foundVessel = vessels.find(
                    (vessel: any) => vessel.id === vesselID,
                )
                setCurrentVessel(foundVessel || null)
                foundVessel?.id > 0 &&
                    getVesselStatus({ variables: { id: +vesselID } })
            }
        }
    }, [vessels])

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
            if (hasPermission('DELETE_TASK', permissions)) {
                setDelete_task(true)
            } else {
                setDelete_task(false)
            }
            if (hasPermission('COMPLETE_TASK', permissions)) {
                setEdit_recurring_task(true)
                setComplete_task(true)
            }
            if (hasPermission('EDIT_RECURRING_TASK', permissions)) {
                setEdit_recurring_task(true)
                setComplete_task(true)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        const vesselId =
            maintenanceChecks?.basicComponentID > 0
                ? maintenanceChecks.basicComponentID
                : maintenanceChecks?.inventory?.vessel?.id
        if (+vesselId !== +vesselStatus?.id) {
            vesselId > 0 && getVesselStatus({ variables: { id: +vesselId } })
        }
    }, [vessels, maintenanceChecks])

    const tabClasses = {
        inactive:
            'inline-flex items-center px-4 py-3 border  rounded-md    hover:0 hover: w-full ring-1 ring-transparent  ',
        active: 'inline-flex items-center px-4 py-3 border  rounded-md   w-full ',
    }

    const handleSetMemberList = (members: any) => {
        setMembers(
            members
                ?.filter(
                    (member: any) =>
                        member.archived == false && member.firstName != '',
                )
                ?.map((member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                })),
        )
    }

    getSeaLogsMembersList(handleSetMemberList)

    const handleCommentTimeChange = (date: any) => {
        setCommentTime(date)
    }

    const handleScheduleCompletedDateChange = (newValue: any) => {
        setScheduleCompletedDate(newValue)
    }

    const handleEditorChange = (value: any) => {
        setContent(value)
    }

    const handleReviewEditorChange = (value: any) => {
        setReviewContent(value)
    }

    const handleSubtaskEditorChange = (value: any) => {
        setSubtaskContent(value)
    }

    const handleSetSubTask = (data: any) => {
        setSubTask(data)
    }

    getMaintenanceCheckSubTaskByID(taskId, handleSetSubTask)

    getSeaLogsMembersList(setCrewMembers)

    // const handleSetInventories = (data: any) => {
    //     setAllInventories(data)
    //     if (maintenanceChecks?.basicComponentID > 0) {
    //         const filteredInvenventories = data.filter((inventory: any) => {
    //             return (
    //                 inventory.vesselID === maintenanceChecks?.basicComponentID
    //             )
    //         })
    //         setInventories(filteredInvenventories)
    //     } else {
    //         setInventories(data)
    //     }
    // }

    const [queryInventories] = useLazyQuery(GET_INVENTORY_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventoryList[0].list
            if (data) {
                setInventories(data)
            }
        },
        onError: (error: any) => {
            console.error('queryInventoriesEntry error', error)
        },
    })

    const [queryInventoriesByVessel] = useLazyQuery(GET_INVENTORY_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventoryList[0].list
            if (data) {
                setInventories(
                    data.filter(
                        (inventory: any) =>
                            inventory.vessel.id == loadedInventory,
                    ),
                )
                setInventories(
                    data.filter(
                        (inventory: any) =>
                            inventory.vessel.id == loadedInventory,
                    ),
                )
            }
        },
        onError: (error: any) => {
            console.error('queryInventoriesEntry error', error)
        },
    })

    const loadInventories = async () => {
        const vesselID =
            currentTask?.basicComponentID > 0
                ? currentTask.basicComponentID
                : maintenanceChecks?.basicComponentID
        if (vesselID > 0) {
            if (loadedInventory != vesselID) {
                setLoadedInventory(vesselID)
                queryInventoriesByVessel({
                    variables: {
                        vesselID: +vesselID,
                    },
                })
                // await queryInventories({
                //     variables: {
                //         filter: {
                //             vesselID: {
                //                 eq: vesselID,
                //             },
                //         },
                //     },
                // })
            }
        } else {
            setLoadedInventory(0)
            await queryInventories({
                variables: {
                    vesselID: 0,
                },
            })
        }
    }

    useEffect(() => {
        loadInventories()
    }, [maintenanceChecks, currentTask])

    const handleVesselList = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const appendedData = activeVessels.map((item: any) => ({
            ...item,
        }))
        appendedData.push({ title: 'Other', id: 0 })
        setVessels(appendedData)
    }

    getVesselList(handleVesselList)

    const handleSetMaintenanceChecks = (data: any) => {
        setCurrentMaintenanceCheck(data)
        data?.startDate && setStartDate(dayjs(data.startDate))
        {
            data?.expires &&
                data?.maintenanceScheduleID == 0 &&
                setExpiryDate(dayjs(data.expires))
        }
        {
            data?.dateCompleted
                ? setCompletionDate(dayjs(data.dateCompleted))
                : data?.completed && setCompletionDate(dayjs(data.completed))
        }
        if (data?.maintenanceScheduleID > 0) {
            setDisplayRecurringTasks(true)
            setScheduleEvery(data?.maintenanceSchedule?.occursEvery)
            setRecurringTasks(data?.maintenanceSchedule)
            upcomingScheduleDate(data)
            data?.basicComponentID > 0 &&
                getEngineIdsByVessel({
                    variables: {
                        id: +data?.basicComponentID,
                    },
                })
        }
        setMaintenanceChecks(data)
        setInventoryDefaultValue(
            data?.inventory?.id > 0
                ? {
                      label: data.inventory.item,
                      value: data.inventory.id,
                  }
                : {
                      label: null,
                      value: '0',
                  },
        )
        setSignature(data?.maintenanceCheck_Signature)
        {
            data?.documents.nodes?.length > 0 &&
                getFiles(
                    data?.documents.nodes
                        ?.map((link: any) => link.id)
                        .join(','),
                )
        }
        {
            data?.attachmentLinks?.nodes &&
                setLinkSelectedOption(
                    data?.attachmentLinks?.nodes.map((link: any) => ({
                        label: link.link,
                        value: link.id,
                    })),
                )
        }
        setCurrentTask({
            ...currentTask,
            status: data?.status.replaceAll('_', ' '),
        })
        setIsCompleted(data?.status == 'Completed')
        const difference =
            parseInt(data?.projected ?? '0') - parseInt(data?.actual ?? '0')
        setCostsDifference(difference)
        data?.documents?.nodes?.length > 0 &&
            setDocuments(data?.documents?.nodes)
        data?.comments ? setContent(data?.comments) : setContent('')
        data?.R2File?.nodes?.length > 0 && setAttachments(data?.R2File?.nodes)
    }

    const updateCostsDifference = (e: any) => {
        const projectedElement = document.getElementById(
            'task-projected',
        ) as HTMLInputElement
        const actualElement = document.getElementById(
            'task-actual',
        ) as HTMLInputElement

        const projected = projectedElement.value ? +projectedElement.value : 0
        const actual = actualElement.value ? +actualElement.value : 0

        setCostsDifference(projected - actual)
    }

    getMaintenanceCheckByID(taskId, handleSetMaintenanceChecks)

    const getFiles = async (ids: string) => {
        await queryFiles({
            variables: {
                id: ids,
            },
        })
    }

    const [queryFiles] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const { isSuccess, data } = response.getFile
            if (isSuccess) {
                documents.push(data)
                setDocuments(documents)
            }
        },
        onError: (error: any) => {
            console.error('queryFilesEntry error', error)
        },
    })

    const onChangeComplete = (event: any) => {
        if (event === true) {
            setCurrentTask({
                ...currentTask,
                status: 'Completed',
            })
            setIsCompleted(true)
        }
    }

    const [createSeaLogsFileLinks] = useMutation(CREATE_SEALOGS_FILE_LINKS, {
        onCompleted: (response: any) => {
            const data = response.createSeaLogsFileLinks
            if (data.id > 0) {
                const newLinks = [...fileLinks, data]
                setFileLinks(newLinks)
                linkSelectedOption
                    ? setLinkSelectedOption([
                          ...linkSelectedOption,
                          { label: data.link, value: data.id },
                      ])
                    : setLinkSelectedOption([
                          { label: data.link, value: data.id },
                      ])
            }
        },
        onError: (error: any) => {
            console.error('createSeaLogsFileLinksEntry error', error)
        },
    })

    const priorityOptions = [
        { value: 'None', label: 'None', color: '#1f2937' },
        { value: 'Low', label: 'Low', color: '#15803d' },
        { value: 'Medium', label: 'Medium', color: '#f97316' },
        { value: 'High', label: 'High', color: '#e11d48' },
    ]

    const recurringType = [
        { label: 'Engine Hours', value: 'Hours' },
        { label: 'Days', value: 'Days' },
        { label: 'Weeks', value: 'Weeks' },
        { label: 'Months', value: 'Months' },
        { label: 'Number of sailings', value: 'Uses' },
    ]

    const colourStyles: StylesConfig = {
        option: (
            styles: any,
            {
                data,
                isDisabled,
                isFocused,
                isSelected,
            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },
        ) => {
            const color = data.color
            return {
                ...styles,
                backgroundColor: isDisabled
                    ? undefined
                    : isSelected
                      ? data.color + '20'
                      : isFocused
                        ? data.color + '20'
                        : undefined,
                color: data.color,
            }
        },
        singleValue: (styles: any, data: any) => ({
            ...styles,
            color: priorityOptions.find(
                (option: any) => option.value == data.data.value,
            )?.color,
        }),
    }

    const statusOptions = [
        { value: 'Open', label: 'Open' },
        { value: 'Save As Draft', label: 'Save as Draft' },
        { value: 'In Progress', label: 'In Progress' },
        { value: 'On Hold', label: 'On Hold' },
        { value: 'Completed', label: 'Completed' },
    ]

    const subtaskProgress = Number(
        (subTasks.filter((subtask: any) => subtask.status == 'Completed')
            .length /
            subTasks.length) *
            100,
    )

    // Wrapper function for ActionFooter
    const handleUpdateTask = () => {
        handleUpdate({})
    }

    const handleUpdate = async (e: any) => {
        if (!edit_task) {
            toast({
                title: 'Error',
                description: 'You do not have permission to edit this task',
                variant: 'destructive',
            })
            return
        }
        if (displayRecurringTasks && !edit_recurring_task) {
            toast({
                title: 'Error',
                description:
                    'You do not have permission to edit this recurring task',
                variant: 'destructive',
            })
            return
        }
        if (displayRecurringTasks && typeof e === 'object') {
            //const title = (
            //    document.getElementById('task-title') as HTMLInputElement
            //).value
            const titleElement = document.getElementById(
                'task-title',
            ) as HTMLInputElement
            const title = titleElement ? titleElement.value : '' // Add null checks
            //const frequency = (
            //    document.getElementById('task-frequency') as HTMLInputElement
            //).value
            // const frequencyElement = document.getElementById(
            //     'task-frequency',
            // ) as HTMLInputElement
            const frequency =
                scheduleEvery > 0 ? scheduleEvery : currentTask.occursEvery

            if (isNaN(+frequency) || +frequency <= 0) {
                setActiveTab('Recurring schedule')
                toast({
                    title: 'Error',
                    description:
                        'You need to set the schedule of this recurring task. Please go to the "Recurring schedule" tab and set the frequency.',
                    variant: 'destructive',
                })
                return
            }
            const type = currentTask.occursEveryType
            const scheduleType =
                currentTask.occursEveryType === 'Hours'
                    ? 'DutyHours'
                    : currentTask.occursEveryType === 'Uses'
                      ? 'EquipmentUsages'
                      : 'Frequency'
            //const description = (
            //    document.getElementById(
            //        'recurring-task-description',
            //    ) as HTMLInputElement
            //).value
            const descriptionElement = document.getElementById(
                'recurring-task-description',
            ) as HTMLInputElement
            const description = descriptionElement
                ? descriptionElement.value
                : ''

            //const highWarnWithin = (
            //    document.getElementById('high-warn-within') as HTMLInputElement
            //)?.value
            const highWarnWithinElement = document.getElementById(
                'high-warn-within',
            ) as HTMLInputElement
            const highWarnWithin = highWarnWithinElement
                ? highWarnWithinElement.value
                : ''

            //const mediumWarnWithin = (
            //    document.getElementById(
            //        'medium-warn-within',
            //    ) as HTMLInputElement
            //)?.value
            const mediumWarnWithinElement = document.getElementById(
                'medium-warn-within',
            ) as HTMLInputElement
            const mediumWarnWithin = mediumWarnWithinElement
                ? mediumWarnWithinElement.value
                : ''

            //const lowWarnWithin = (
            //    document.getElementById('low-warn-within') as HTMLInputElement
            //)?.value
            const lowWarnWithinElement = document.getElementById(
                'low-warn-within',
            ) as HTMLInputElement
            const lowWarnWithin = lowWarnWithinElement
                ? lowWarnWithinElement.value
                : ''

            var schedule: any = {
                title: title,
                description: description,
                type: scheduleType,
                occursEveryType: type,
                highWarnWithin: +highWarnWithin,
                mediumWarnWithin: +mediumWarnWithin,
                lowWarnWithin: +lowWarnWithin,
                warnWithinType: type,
                maintenanceChecks: maintenanceChecks.id,
                basicComponents: currentTask.basicComponentID
                    ? currentTask.basicComponentID
                    : maintenanceChecks.basicComponentID,
                inventoryID:
                    currentTask.InventoryID >= 0
                        ? currentTask.InventoryID
                        : maintenanceChecks.inventoryID,
            }

            if (recurringTasks?.id > 0) {
                schedule.id = recurringTasks.id
            }

            if (frequency) {
                schedule.occursEvery = +frequency
            }

            {
                recurringTasks?.id > 0
                    ? await updateMaintenanceSchedule({
                          variables: {
                              input: schedule,
                          },
                      })
                    : await createMaintenanceSchedule({
                          variables: {
                              input: schedule,
                          },
                      })
            }
        } else {
            //const name = (
            //    document.getElementById('task-name') as HTMLInputElement
            //)?.value
            const nameElement = document.getElementById(
                'task-name',
            ) as HTMLInputElement
            const name = nameElement ? nameElement.value : false
            if (!name) {
                toast({
                    title: 'Error',
                    description: 'You need to set the name of this task',
                    variant: 'destructive',
                })
                return
            }

            //const workorder = (
            //    document.getElementById('task-workorder') as HTMLInputElement
            //).value
            const workorderElement = document.getElementById(
                'task-workorder',
            ) as HTMLInputElement
            const workorder = workorderElement ? workorderElement.value : ''

            const description = content
            //const projected = (
            //    document.getElementById('task-projected') as HTMLInputElement
            //).value
            const projectedElement = document.getElementById(
                'task-projected',
            ) as HTMLInputElement
            const projected = projectedElement ? projectedElement.value : ''

            //const actual = (
            //    document.getElementById('task-actual') as HTMLInputElement
            //).value
            const actualElement = document.getElementById(
                'task-actual',
            ) as HTMLInputElement
            const actual = actualElement ? actualElement.value : ''

            //const difference = (
            //    document.getElementById('task-difference') as HTMLInputElement
            //).value
            const differenceElement = document.getElementById(
                'task-difference',
            ) as HTMLInputElement
            const difference = differenceElement ? differenceElement.value : ''

            await updateMaintenanceChecks({
                variables: {
                    input: {
                        id: maintenanceChecks.id,
                        workOrderNumber: workorder,
                        projected: projected
                            ? +projected
                            : +maintenanceChecks.projected,
                        actual: actual ? +actual : +maintenanceChecks.actual,
                        difference: difference
                            ? +difference
                            : +maintenanceChecks.difference,
                        name: name,
                        startDate: startDate
                            ? dayjs(startDate).format('YYYY-MM-DD')
                            : maintenanceChecks.startDate,
                        completed: expiryDate
                            ? expiryDate === 'Invalid Date'
                                ? maintenanceChecks.completed
                                : expiryDate
                            : maintenanceChecks.completed,
                        completedByID: currentTask.completedBy,
                        dateCompleted: completionDate
                            ? completionDate
                            : maintenanceChecks.dateCompleted,
                        expires:
                            expiryDate === 'Invalid Date' ? null : expiryDate,
                        comments: description,
                        severity: currentTask.severity,
                        maintenanceCategoryID: currentTask.category,
                        status: currentTask.status
                            ? currentTask.status
                            : maintenanceChecks.status.replaceAll('_', ' '),
                        documents:
                            documents.length > 0
                                ? documents
                                      ?.map((doc: any) => +doc.id)
                                      .join(',')
                                : maintenanceChecks.documents?.nodes
                                      .map((doc: any) => +doc.id)
                                      .join(','),
                        attachmentLinks: linkSelectedOption
                            ? linkSelectedOption
                                  .map((link: any) => link.value)
                                  .join(',')
                            : maintenanceChecks.attachmentLinks?.nodes
                                  .map((link: any) => link.id)
                                  .join(','),
                        assignees: currentTask.assignees
                            ? currentTask.assignees
                            : maintenanceChecks.assignees?.nodes
                                  .map((assignee: any) => assignee.id)
                                  .join(','),
                        assignedToID: currentTask.assignees
                            ? +currentTask.assignees
                            : maintenanceChecks?.assignedToID,
                        basicComponentID: currentTask.basicComponentID
                            ? currentTask.basicComponentID
                            : maintenanceChecks.basicComponentID,
                        inventoryID:
                            currentTask.inventoryID >= 0
                                ? currentTask.inventoryID
                                : maintenanceChecks.inventoryID,
                        maintenanceCheck_SignatureID:
                            maintenanceChecks.maintenanceCheck_SignatureID,
                        recurringID:
                            +maintenanceChecks.recurringID === 0 &&
                            recurringTasks
                                ? taskId
                                : maintenanceChecks.recurringID,
                    },
                },
            })
        }
    }

    const [createMaintenanceSchedule] = useMutation(
        CREATE_COMPONENT_MAINTENANCE_SCHEDULE,
        {
            onCompleted: (response: any) => {
                const data = response.createComponentMaintenanceSchedule
                if (data.id > 0) {
                    handleUpdate(true)
                }
            },
            onError: (error: any) => {
                handleUpdate(true)
                console.error('createMaintenanceScheduleEntry error', error)
            },
        },
    )

    const [createBlankMaintenanceSchedule] = useMutation(
        CREATE_COMPONENT_MAINTENANCE_SCHEDULE,
        {
            onCompleted: (response: any) => {
                const data = response.createComponentMaintenanceSchedule
                updateWithoutCreateMaintenanceChecks({
                    variables: {
                        input: {
                            id: taskId,
                            maintenanceScheduleID: data.id,
                        },
                    },
                })
                setRecurringTasks(data)
            },
            onError: (error: any) => {
                console.error('createMaintenanceScheduleEntry error', error)
            },
        },
    )

    const [updateWithoutCreateMaintenanceChecks] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_CHECK,
        {
            onCompleted: (response: any) => {
                const data = response.updateComponentMaintenanceCheck
            },
            onError: (error: any) => {
                console.error('updateMaintenanceChecksEntry error', error)
            },
        },
    )

    const [updateMaintenanceSchedule] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_SCHEDULE,
        {
            onCompleted: (response: any) => {
                const data = response.updateComponentMaintenanceSchedule
                if (data.id > 0) {
                    handleUpdate(true)
                }
            },
            onError: (error: any) => {
                handleUpdate(true)
                console.error('updateMaintenanceScheduleEntry error', error)
            },
        },
    )

    const [updateMaintenanceChecks] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_CHECK,
        {
            onCompleted: (response: any) => {
                const data = response.updateComponentMaintenanceCheck
                if (
                    displayRecurringTasks &&
                    maintenanceChecks.status != 'Completed' &&
                    currentTask.status == 'Completed'
                ) {
                    router.push(
                        '/maintenance/complete-recurring-task?taskID=' + taskId,
                    )
                } else if (data.id > 0) {
                    if (
                        searchParams.get('taskCreated') ||
                        searchParams.get('taskCompleted')
                    ) {
                        if (redirectTo === 'inventory') {
                            router.push(
                                `/inventory/view?id=${
                                    currentTask.inventoryID
                                        ? currentTask.inventoryID
                                        : maintenanceChecks.inventoryID
                                }&inventoryTab=maintenance`,
                            )
                        } else {
                            searchParams.get('redirect_to')
                                ? router.push(
                                      searchParams?.get('redirect_to') + '',
                                  )
                                : router.push('/maintenance')
                        }
                    } else {
                        if (!inSidebar) {
                            searchParams.get('redirect_to')
                                ? router.push(
                                      searchParams?.get('redirect_to') + '',
                                  )
                                : router.push('/maintenance')
                        } else {
                            onSidebarClose && onSidebarClose()
                        }
                    }
                }
            },
            onError: (error: any) => {
                console.error('updateMaintenanceChecksEntry error', error)
            },
        },
    )

    const handleCancel = () => {
        if (!inSidebar) {
            if (
                searchParams.get('taskCreated') ||
                searchParams.get('taskCompleted')
            ) {
                searchParams.get('redirect_to')
                    ? router.push(searchParams?.get('redirect_to') + '')
                    : router.push('/maintenance')
            } else {
                router.back()
            }
        } else {
            onSidebarClose && onSidebarClose()
        }
    }

    // const onSignatureChanged = async (sign: any) => {
    //     if (maintenanceChecks.MaintenanceCheck_SignatureID > 0) {
    //         var newSignature = { ...signature }
    //         newSignature.SignatureData = sign
    //         setSignature(newSignature)
    //         await updateSignature({
    //             variables: {
    //                 input: {
    //                     id: newSignature.id,
    //                     signatureData: newSignature.signatureData,
    //                     maintenanceCheckId: newSignature.maintenanceCheckID,
    //                     memberId: newSignature.memberID,
    //                 },
    //             },
    //         })
    //     }
    // }

    // const [updateSignature] = useMutation(
    //     UPDATE_COMPONENT_MAINTENANCE_SIGNATURE,
    //     {
    //         onCompleted: (response: any) => {
    //             const data = response.updateMaintenanceCheck_Signature
    //         },
    //         onError: (error: any) => {
    //             console.error('updateSignatureEntry error', error)
    //         },
    //     },
    // )

    const handleExpiryChange = (newValue: any) => {
        setExpiryDate(newValue)
    }

    const handleCompletionChange = (newValue: any) => {
        setCompletionDate(newValue)
    }

    const handleStartDateChange = (newValue: any) => {
        setStartDate(newValue)
        updateDueDate(newValue)
        setIsLastCompletedDateChanged(true)
    }

    const handleDeleteCheck = async () => {
        if (!delete_task) {
            onSidebarClose && onSidebarClose()
            toast({
                title: 'Error',
                description: 'You do not have permission to delete this task',
                variant: 'destructive',
            })
            return
        }
        if (displayRecurringTasks && !edit_recurring_task) {
            toast({
                title: 'Error',
                description: 'You do not have permission to delete this task',
                variant: 'destructive',
            })
            return
        }
        await deleteMaintenanceCheck({
            variables: {
                id: [+taskId],
            },
        })
    }

    const [deleteMaintenanceCheck] = useMutation(
        DELETE_COMPONENT_MAINTENANCE_CHECK,
        {
            onCompleted: (response: any) => {
                if (!inSidebar) {
                    router.back()
                } else {
                    onSidebarClose && onSidebarClose()
                }
            },
            onError: (error: any) => {
                console.error('deleteMaintenanceCheckEntry error', error)
            },
        },
    )

    const handleCreateSubTask = () => {
        const subTaskName = (
            document.getElementById('subtask-name') as HTMLInputElement
        ).value
        setOpenSubTaskDialog(false)
        createSubtask({
            variables: {
                input: {
                    task: subTaskName,
                    description: subtaskContent,
                    inventoryID: subtaskInventoryValue,
                },
            },
        })
    }

    const [createSubtask] = useMutation(CREATE_COMPONENT_MAINTENANCE_SUBTASK, {
        onCompleted: (response: any) => {
            const data = response.createMaintenanceScheduleSubTask
            if (data) {
                createSubtaskCheck({
                    variables: {
                        input: {
                            maintenanceScheduleSubTaskID: data.id,
                            componentMaintenanceCheckID: taskId,
                            status: 'In Review',
                        },
                    },
                })
            }
        },
        onError: (error: any) => {
            console.error('createSubtaskEntry error', error)
        },
    })

    const [queryMaintenanceCheckSubTask] = useLazyQuery(
        GET_MAINTENANCE_CHECK_SUBTASK,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readMaintenanceCheckSubTasks.nodes
                if (data) {
                    setSubTask(data)
                }
            },
            onError: (error: any) => {
                console.error('queryMaintenanceCheckSubTask error', error)
            },
        },
    )

    const loadMaintenanceCheckSubTask = async () => {
        await queryMaintenanceCheckSubTask({
            variables: {
                id: +taskId,
            },
        })
    }

    const [createSubtaskCheck] = useMutation(CREATE_MAINTENANCE_CHECK_SUBTASK, {
        onCompleted: (response: any) => {
            const data = response.createMaintenanceCheckSubTask
            if (+data.id > 0) {
                loadMaintenanceCheckSubTask()
            }
        },
        onError: (error: any) => {
            console.error('createSubtaskCheckEntry error', error)
        },
    })

    useEffect(() => {
        if (attachments.length > 0) {
            attachments.map((receipt: any) => {
                if (!receipt.id && taskId > 0) {
                    createAttachments({
                        variables: {
                            input: {
                                title: receipt.title,
                                componentMaintenanceCheckID: taskId,
                            },
                        },
                    })
                }
            })
        }
    }, [attachments])

    const [createAttachments] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = attachments.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setAttachments(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    useEffect(() => {
        if (subTaskAttachments.length > 0) {
            subTaskAttachments.map((receipt: any) => {
                if (!receipt.id && currentSubTaskCheckID > 0) {
                    createSubTaskAttachments({
                        variables: {
                            input: {
                                title: receipt.title,
                                maintenanceCheckSubTaskID:
                                    currentSubTaskCheckID,
                            },
                        },
                    })
                }
            })
        }
    }, [subTaskAttachments])

    const [createSubTaskAttachments] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = attachments.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setSubTaskAttachments(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    const handleDisplayRecurringTasks = (e: any) => {
        if (!edit_recurring_task) {
            toast({
                title: 'Error',
                description:
                    'You do not have permission to change recurring tasks',
                variant: 'destructive',
            })
            return
        }
        setDisplayRecurringTasks(e.target.checked)
        if (!recurringTasks?.id) {
            createBlankMaintenanceSchedule({
                variables: {
                    input: {},
                },
            })
        }
        if (e.target.checked === false && recurringTasks?.id) {
            updateWithoutCreateMaintenanceChecks({
                variables: {
                    input: {
                        id: taskId,
                        maintenanceScheduleID: 0,
                    },
                },
            })
        }
    }

    const updateDueDate = (newStartDate: any = false) => {
        const occursEveryType =
            currentMaintenanceCheck.maintenanceSchedule.occursEveryType
        if (occursEveryType === 'Hours' || occursEveryType === 'Uses') {
            if (occursEveryType === 'Uses') {
                // setExpiryDate(dayjs(maintenanceChecks.equipmentUsagesAtCheck).format('DD/MM/YYYY'))
            }
            // setExpiryDate(dayjs(maintenanceChecks.dutyHoursAtCheck).format('DD/MM/YYYY'))
        } else {
            const occursEvery = scheduleEvery
            const lastCompletedDate = dayjs(
                startDate &&
                    new Date(dayjs(startDate).toISOString()).getTime() > 0
                    ? new Date(newStartDate ? newStartDate : startDate)
                    : new Date(),
            ).startOf('day')
            const nextOccurrence = lastCompletedDate.add(
                occursEvery,
                occursEveryType?.slice(0, -1).toLowerCase(),
            )
            if (nextOccurrence.format('DD/MM/YYYY') != 'Invalid Date') {
                setExpiryDate(nextOccurrence)
            }
        }
    }

    const upcomingScheduleDate = (maintenanceChecks: any) => {
        setCurrentTask({
            ...currentTask,
            occursEveryType:
                maintenanceChecks?.maintenanceSchedule?.occursEveryType,
        })
        const recurringTasks = maintenanceChecks?.maintenanceSchedule
        if (maintenanceChecks?.maintenanceSchedule?.id > 0) {
            const occursEveryType = recurringTasks.occursEveryType
                ? recurringTasks.occursEveryType
                : 'Days'
            if (occursEveryType === 'Hours' || occursEveryType === 'Uses') {
                if (occursEveryType === 'Uses') {
                    return (
                        maintenanceChecks.equipmentUsagesAtCheck +
                        ' Equipment Uses'
                    )
                }
                // setExpiryDate(dayjs(maintenanceChecks.dutyHoursAtCheck).format('DD/MM/YYYY'))
                // return maintenanceChecks.dutyHoursAtCheck + ' Engine Hours'
            } else {
                const occursEvery = recurringTasks.occursEvery
                    ? recurringTasks.occursEvery
                    : 1
                const lastCompletedDate = dayjs(
                    maintenanceChecks?.startDate
                        ? new Date(maintenanceChecks.startDate)
                        : new Date(),
                ).startOf('day')
                const nextOccurrence = lastCompletedDate.add(
                    occursEvery,
                    occursEveryType,
                )
                setExpiryDate(nextOccurrence)
                return nextOccurrence.format('DD/MM/YYYY')
            }
        }
        // return dayjs().format('DD/MM/YYYY')
    }

    const lastScheduleDate = () => {
        if (recurringTasks) {
            const occursEveryType = recurringTasks.occursEveryType
                ? recurringTasks.occursEveryType
                : 'Days'
            if (occursEveryType !== 'Hours' && occursEveryType !== 'Uses') {
                const lastCompleted = maintenanceChecks?.startDate
                    ? dayjs(maintenanceChecks.startDate)
                    : dayjs()
                return lastCompleted
            }
        }
        return dayjs()
    }

    const handleUpdateSubTask = (e: any) => {
        if (e?.target?.checked) {
            setCurrentSubTaskCheckID(e.target.id)
            setCurrentSubTask(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.id,
            )
            setSubtaskContent(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.description,
            )
            setSubtaskInventoryValue(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.inventoryID,
            )
            subTasks.find((subtask: any) => subtask.id == e.target.id)?.R2File
                ?.nodes?.length > 0
                ? setSubTaskAttachments(
                      subTasks.find((subtask: any) => subtask.id == e.target.id)
                          .R2File.nodes,
                  )
                : setSubTaskAttachments([])
            setDisplayUpdateSubTask(true)
            setAlertSubTaskStatus(false)
        }
        if (e?.target?.checked === false) {
            setAlertSubTaskStatus(true)
            setCurrentSubTask(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.id,
            )
            setCurrentSubTaskCheckID(e.target.id)
            setSubtaskContent(
                subTasks.filter((subtask: any) => subtask.id == e.target.id)[0]
                    .maintenanceScheduleSubTask.description,
            )
            setSubtaskInventoryValue(
                subTasks.find((subtask: any) => subtask.id == e.target.id)
                    .maintenanceScheduleSubTask.inventoryID,
            )
            subTasks.find((subtask: any) => subtask.id == e.target.id)?.R2File
                ?.nodes?.length > 0
                ? setSubTaskAttachments(
                      subTasks.find((subtask: any) => subtask.id == e.target.id)
                          .R2File.nodes,
                  )
                : setSubTaskAttachments([])
            setDisplayUpdateSubTask(true)
        }
        if (e === 'updateFindings') {
            const subtaskFindings = (
                document.getElementById('subtask-findings') as HTMLInputElement
            ).value
            setDisplayAddFindings(false)
            setAuthorID(0)
            setScheduleCompletedDate('')
            updateSubtaskCheck({
                variables: {
                    input: {
                        id: currentSubTaskCheckID,
                        findings: subtaskFindings,
                        completedByID: authorID,
                        dateCompleted: scheduleCompletedDate,
                    },
                },
            })
        }
        if (e === 'updateSubTask') {
            const subTaskName = (
                document.getElementById('subtask-name') as HTMLInputElement
            ).value
            setDisplayUpdateSubTask(false)
            const dateCompleted = subTasks.find(
                (subtask: any) => subtask.id === currentSubTaskCheckID,
            )?.dateCompleted
                ? dayjs(
                      subTasks.find(
                          (subtask: any) =>
                              subtask.id === currentSubTaskCheckID,
                      ).dateCompleted,
                  )
                : ''
            alertSubTaskStatus
                ? (setDisplayAddFindings(true),
                  setAuthorID(
                      subTasks.find(
                          (subtask: any) =>
                              subtask.id === currentSubTaskCheckID,
                      )?.completedBy?.id,
                  ),
                  setScheduleCompletedDate(dateCompleted))
                : setDisplayAddFindings(false)
            updateSubtask({
                variables: {
                    input: {
                        id: currentSubTask,
                        task: subTaskName,
                        description: subtaskContent,
                        inventoryID: subtaskInventoryValue,
                    },
                },
            })
            updateSubtaskCheck({
                variables: {
                    input: {
                        id: currentSubTaskCheckID,
                        status: alertSubTaskStatus ? 'Completed' : 'In Review',
                    },
                },
            })
        }
        if (e === 'deleteSubTask') {
            const subtaskFindings = subTasks.filter(
                (subtask: any) => subtask.id == currentSubTaskCheckID,
            )[0].findings
            updateSubtaskCheck({
                variables: {
                    input: {
                        id: currentSubTaskCheckID,
                        componentMaintenanceCheckID: '0',
                        findings:
                            subtaskFindings +
                            'deleted from task ' +
                            currentSubTaskCheckID,
                    },
                },
            })
            setDisplayUpdateSubTask(false)
        }
    }

    const [updateSubtask] = useMutation(UPDATE_COMPONENT_MAINTENANCE_SUBTASK, {
        onCompleted: (response: any) => {
            const data = response.updateMaintenanceScheduleSubTask
            if (data.id > 0) {
                loadMaintenanceCheckSubTask()
            }
        },
        onError: (error: any) => {
            console.error('updateSubtaskEntry error', error)
        },
    })

    const [updateSubtaskCheck] = useMutation(
        UPDATE_COMPONENT_MAINTENANCE_CHECK_SUBTASK,
        {
            onCompleted: (response: any) => {
                const data = response.updateMaintenanceCheckSubTask
                if (data.id > 0) {
                    loadMaintenanceCheckSubTask()
                }
            },
            onError: (error: any) => {
                console.error('updateSubtaskCheckEntry error', error)
            },
        },
    )

    const handleSetDisplayAddFindings = (subTaskID: any, e: any) => {
        setDisplayUpdateFindings(false)
        setCurrentSubTaskCheckID(subTaskID)
        setAuthorID(
            subTasks.find((subtask: any) => subtask.id === subTaskID)
                ?.completedBy?.id,
        )
        const dateCompleted = subTasks.find(
            (subtask: any) => subtask.id === subTaskID,
        )?.dateCompleted
            ? dayjs(
                  subTasks.find((subtask: any) => subtask.id === subTaskID)
                      .dateCompleted,
              )
            : ''
        setScheduleCompletedDate(dateCompleted)
        setDisplayAddFindings(true)
    }

    const handleDisplayWarnings = (e: any) => {
        setDisplayWarnings(e.target.checked)
    }

    const deleteFile = async (id: number) => {
        const newDocuments = documents.filter((doc: any) => doc.id !== id)
        setDocuments(newDocuments)
    }

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })
    const loadCrewMemberInfo = async (crewId: any) => {
        await queryCrewMemberInfo({
            variables: {
                crewMemberIDs: crewId.length > 0 ? crewId : [0],
            },
        })
    }
    const [readComponentMaintenanceChecks] = useLazyQuery(
        GET_MAINTENANCE_CHECK,
        {
            fetchPolicy: 'no-cache',
            onCompleted: (response: any) => {
                const data = response.readComponentMaintenanceChecks
                if (data.nodes.length > 0) {
                    const crewIds: number[] = Array.from(
                        new Set(
                            data.nodes
                                .filter((item: any) => item.assignedToID > 0)
                                .map((item: any) => item.assignedToID),
                        ),
                    )
                    loadCrewMemberInfo(crewIds)
                    var maintenanceChecksArray = data.nodes.map(
                        (maintenanceCheck: any) => {
                            return {
                                id: maintenanceCheck.id,
                                name: maintenanceCheck.name,
                                basicComponentID:
                                    maintenanceCheck.basicComponentID,
                                comments: maintenanceCheck.comments,
                                description: maintenanceCheck.description,
                                assignedToID: maintenanceCheck.assignedToID,
                                expires: maintenanceCheck.expires, // the value of maintenanceCheck.expires here is already computed from upcomingScheduleDate()
                                status: maintenanceCheck.status,
                                startDate: maintenanceCheck.startDate,
                                isOverDue: isOverDueTask(maintenanceCheck),
                                isCompleted:
                                    maintenanceCheck.status === 'Completed'
                                        ? '1'
                                        : '2',
                            }
                        },
                    )
                    // Completed: sort by "expires" from recent to oldest
                    maintenanceChecksArray.sort((a: any, b: any) => {
                        if (a.isCompleted === '1' && b.isCompleted === '1') {
                            if (a.expires === 'NA' && b.expires !== 'NA') {
                                return 1
                            } else if (
                                a.expires !== 'NA' &&
                                b.expires === 'NA'
                            ) {
                                return -1
                            } else {
                                return (
                                    new Date(b.expires).getTime() -
                                    new Date(a.expires).getTime()
                                )
                            }
                        } else if (a.isCompleted === '1') {
                            return 1
                        } else if (b.isCompleted === '1') {
                            return -1
                        } else {
                            return dayjs(a.expires).diff(b.expires)
                        }
                    })
                    setCompletedRecurringTasks(maintenanceChecksArray)
                }
            },
            onError: (error: any) => {
                console.error('readComponentMaintenanceChecks error', error)
            },
        },
    )

    const loadCompletedRecurringTasks = async (recurringID: number) => {
        if (recurringID > 0) {
            await readComponentMaintenanceChecks({
                variables: {
                    filter: {
                        recurringID: {
                            eq: +recurringID,
                        },
                        status: { eq: 'Completed' },
                    },
                },
            })
        }
    }
    const handleOnChangeVessel = (value: any) => {
        setInventoryDefaultValue({
            label: null,
            value: '0',
        })
        setCurrentTask({
            ...currentTask,
            basicComponentID: value?.value,
            inventoryID: 0,
        })
        setInventories(
            allInventories.filter((inventory: any) => {
                return inventory.vesselID === value?.value
            }),
        )
        getEngineIdsByVessel({
            variables: {
                id: +value?.value,
            },
        })
        getVesselStatus({
            variables: {
                id: +value?.value,
            },
        })
    }

    const handleOnChangeInventory = (value: any) => {
        setCurrentTask({
            ...currentTask,
            inventoryID: value?.value,
        })
        setInventoryDefaultValue(value)
    }

    const handleSubtaskOnChangeInventory = (value: any) => {
        setSubtaskInventoryValue(value.value)
    }

    const handleDeleteLink = (link: any) => {
        setLinkSelectedOption(linkSelectedOption.filter((l: any) => l !== link))
    }
    const linkItem = (link: any) => {
        return (
            <div className="flex justify-between align-middle mr-2 w-fit">
                <Link href={link.label} target="_blank" className="ml-2 ">
                    {link.label}
                </Link>
                <div className="ml-2 ">
                    <XCircleIcon
                        className="w-5 h-5 alert cursor-pointer"
                        onClick={() => handleDeleteLink(link)}
                    />
                </div>
            </div>
        )
    }
    useEffect(() => {
        if (maintenanceChecks) {
            loadCompletedRecurringTasks(+maintenanceChecks.recurringID)
        }
        getTaskRecords(taskId)
        getCategoryList({
            variables: {
                clientID: +(localStorage.getItem('clientId') ?? 0),
            },
        })
    }, [maintenanceChecks])

    const [getCategoryList] = useLazyQuery(GetMaintenanceCategories, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readMaintenanceCategories.nodes
            if (data) {
                setCategoryList(
                    data.map((category: any) => ({
                        label: category.name,
                        value: category.id,
                    })),
                )
            }
        },
        onError: (error) => {
            console.error('Error getting maintenance categories', error)
        },
    })

    const handleSaveRecords = () => {
        const variables = {
            input: {
                commentType: 'General',
                description: reviewContent,
                time: commentTime
                    ? dayjs(commentTime).format('DD/MM/YYYY HH:mm')
                    : dayjs().format('DD/MM/YYYY HH:mm'),
                authorID: commentData?.authorID,
                maintenanceCheckID: taskId,
                subTaskID: displayUpdateSubTask ? currentSubTaskCheckID : 0,
            },
        }
        if (commentData?.id > 0) {
            updateTaskRecord({
                variables: {
                    input: {
                        id: commentData?.id,
                        ...variables.input,
                    },
                },
            })
        } else {
            createTaskRecord({
                variables: {
                    input: {
                        ...variables.input,
                    },
                },
            })
        }
        setOpenRecordsDialog(false)
    }

    const [createTaskRecord] = useMutation(CreateMissionTimeline, {
        onCompleted: (response) => {
            getTaskRecords(taskId)
        },
        onError: (error) => {
            console.error('Error creating Task Record', error)
        },
    })

    const [updateTaskRecord] = useMutation(UpdateMissionTimeline, {
        onCompleted: (response) => {
            getTaskRecords(taskId)
        },
        onError: (error) => {
            console.error('Error updating mission timeline', error)
        },
    })

    const getTaskRecords = (taskId: number) => {
        queryTaskRecords({
            variables: {
                id: taskId,
            },
        })
    }

    const [queryTaskRecords] = useLazyQuery(GetTaskRecords, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readMissionTimelines.nodes
            if (data) {
                setTaskRecords(data)
            }
        },
        onError: (error) => {
            console.error('Error getting task records', error)
        },
    })

    const handleDeleteRecord = () => {
        updateTaskRecord({
            variables: {
                input: {
                    id: +deleteRecordID,
                    archived: true,
                },
            },
        })
        setOpenDeleteRecordDialog(false)
    }

    const taskIsDateType = () => {
        if (currentTask.occursEveryType) {
            if (
                currentTask.occursEveryType === 'Days' ||
                currentTask.occursEveryType === 'Weeks' ||
                currentTask.occursEveryType === 'Months'
            ) {
                return true
            }
        } else {
            if (
                recurringTasks?.occursEveryType === 'Days' ||
                recurringTasks?.occursEveryType === 'Weeks' ||
                recurringTasks?.occursEveryType === 'Months'
            ) {
                return true
            }
        }
        return false
    }

    const taskIsEngineHourType = () => {
        if (currentTask?.occursEveryType) {
            if (currentTask.occursEveryType === 'Hours') {
                return true
            }
        } else {
            if (recurringTasks?.occursEveryType === 'Hours') {
                return true
            }
        }
        return false
    }

    const handleSetOccursEveryType = (value: any) => {
        setEngineList([])
        if (value === 'Days' || value === 'Weeks' || value === 'Months') {
            setCurrentTask({
                ...currentTask,
                occursEveryType: value,
            })
        } else if (
            maintenanceChecks?.basicComponentID > 0 ||
            currentTask?.basicComponentID > 0
        ) {
            setCurrentTask({
                ...currentTask,
                occursEveryType: value,
            })
            getEngineIdsByVessel({
                variables: {
                    id:
                        currentTask?.basicComponentID > 0
                            ? currentTask.basicComponentID
                            : maintenanceChecks.basicComponentID,
                },
            })
        } else {
            setCurrentTask({
                ...currentTask,
                occursEveryType: 'Days',
            })
            toast({
                title: 'Error',
                description: 'Please add a vessel to set this option',
                variant: 'destructive',
            })
        }
    }

    const [getEngineIdsByVessel] = useLazyQuery(GET_ENGINE_IDS_BY_VESSEL, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readBasicComponents.nodes
            const engineIds = data.map((engine: any) => engine.id)
            engineIds &&
                queryGetEngines({
                    variables: {
                        id: engineIds,
                    },
                })
        },
        onError: (error: any) => {
            console.error('getEnginesByVessel error', error)
        },
    })

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngineList(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    const disableSave = () => {
        setSaveDisabled(true)
    }

    const enableSave = () => {
        setSaveDisabled(false)
    }

    const handleCheckEngineCheck = (e: any, engineId: number) => {
        disableSave()
        setDisplayCheckEngineCheck({
            ...displayCheckEngineCheck,
            [engineId]: { value: e.target.checked },
        })
        if (
            recurringTasks?.engineUsage?.nodes?.find(
                (engine: any) => engine.engine.id === engineId,
            )
        ) {
            updateEngineUsage({
                variables: {
                    input: {
                        id: recurringTasks?.engineUsage?.nodes?.find(
                            (engine: any) => engine.engine.id === engineId,
                        ).id,
                        isScheduled: e.target.checked,
                    },
                },
            })
        } else {
            createEngineUsage({
                variables: {
                    input: {
                        engineID: +engineId,
                        maintenanceScheduleID: +recurringTasks.id,
                        isScheduled: e.target.checked,
                    },
                },
            })
        }
    }

    const [createEngineUsage] = useMutation(CreateEngine_Usage, {
        onCompleted: (response: any) => {
            const data = response.createEngine_Usage
            if (data.id > 0) {
                getRecurringTask()
                enableSave()
            }
        },
        onError: (error: any) => {
            console.error('createEngineUsage error', error)
        },
    })

    const [updateEngineUsage] = useMutation(UpdateEngine_Usage, {
        onCompleted: (response: any) => {
            const data = response.updateEngine_Usage
            if (data.id > 0) {
                getRecurringTask()
                enableSave()
            }
        },
        onError: (error: any) => {
            console.error('updateEngineUsage error', error)
        },
    })

    const getRecurringTask = async () => {
        await queryRecurringTask({
            variables: {
                id: +recurringTasks.id,
            },
        })
    }

    const [queryRecurringTask] = useLazyQuery(GET_RECURRING_TASK, {
        onCompleted: (response: any) => {
            const data = response.readOneMaintenanceSchedule
            if (data) {
                setRecurringTasks(data)
            }
        },
        onError: (error: any) => {
            console.error('queryRecurringTask error', error)
        },
    })

    const handleEngineHours = (e: any, engineId: number) => {
        if (
            recurringTasks?.engineUsage?.nodes?.find(
                (engine: any) => engine.engine.id === engineId,
            )
        ) {
            updateEngineUsage({
                variables: {
                    input: {
                        id: recurringTasks?.engineUsage?.nodes?.find(
                            (engine: any) => engine.engine.id === engineId,
                        ).id,
                        lastScheduleHours: +e.target.value,
                    },
                },
            })
        } else {
            createEngineUsage({
                variables: {
                    input: {
                        engineID: engineId,
                        maintenanceScheduleID: recurringTasks.id,
                        lastScheduleHours: +e.target.value,
                    },
                },
            })
        }
    }

    const handleChangeCategpory = (value: any) => {
        if (value.value === 'NEW_CATEGORY') {
            setCreateCategoryDialog(true)
        } else {
            setCurrentTask({
                ...currentTask,
                category: value.value,
            })
        }
    }

    const handleCreateCategory = () => {
        const category = (
            document.getElementById('task-new-category') as HTMLInputElement
        ).value
        createCategory({
            variables: {
                input: {
                    name: category,
                },
            },
        })
    }

    const [createCategory] = useMutation(CREATE_MAINTENANCE_CATEGORY, {
        onCompleted: (response: any) => {
            const data = response.createMaintenanceCategory
            if (data.id > 0) {
                getCategoryList({
                    variables: {
                        clientID: +(localStorage.getItem('clientId') ?? 0),
                    },
                })
                setCreateCategoryDialog(false)
                setCurrentTask({
                    ...currentTask,
                    category: data.id,
                })
            }
        },
        onError: (error: any) => {
            console.error('createCategoryEntry error', error)
        },
    })

    const isDateDisabled = () => {
        return (
            currentTask?.occursEveryType === 'Hours' ||
            currentTask?.occursEveryType === 'Uses' ||
            maintenanceChecks?.maintenanceSchedule?.occursEveryType ===
                'Hours' ||
            maintenanceChecks?.maintenanceSchedule?.occursEveryType === 'Uses'
        )
    }

    const filterInventories = (inventoryData: any) => {
        return inventoryData.filter(
            (inventory: any, index: number, self: any[]) =>
                index ===
                self.findIndex(
                    (i: any) =>
                        i?.item?.toLowerCase() ===
                        inventory?.item?.toLowerCase(),
                ),
        )
    }

    const handleSetVesselStatus = (status: boolean) => {
        setVesselStatus({
            ...vesselStatus,
            status: status ? 'OutOfService' : 'AvailableForVoyage',
        })
        status
            ? createVesselStatus({
                  variables: {
                      input: {
                          vesselID: currentTask?.basicComponentID,
                          date: dayjs().format('YYYY-MM-DD'),
                          status: 'OutOfService',
                          comment: vesselStatus?.comment,
                          reason: 'Other',
                          otherReason:
                              'From Maintenance Task ' +
                              maintenanceChecks?.name +
                              ' on ' +
                              dayjs().format('YYYY-MM-DD'),
                          expectedReturn: expiryDate,
                      },
                  },
              })
            : createVesselStatus({
                  variables: {
                      input: {
                          vesselID: currentTask?.basicComponentID,
                          date: dayjs().format('YYYY-MM-DD'),
                          status: 'AvailableForVoyage',
                      },
                  },
              })
    }

    const handleUpdateVesselStatus = () => {
        {
            vesselStatus?.status === 'OutOfService'
                ? createVesselStatus({
                      variables: {
                          input: {
                              vesselID: vesselStatus?.vesselID,
                              date: vesselStatus?.date,
                              status: vesselStatus?.status,
                              comment: vesselStatus?.comment,
                              reason: vesselStatus?.reason,
                              otherReason: vesselStatus?.otherReason,
                              expectedReturn: vesselStatus?.expectedReturn,
                          },
                      },
                  })
                : createVesselStatus({
                      variables: {
                          input: {
                              vesselID: vesselStatus?.vesselID,
                              date: vesselStatus?.date,
                              status: vesselStatus?.status,
                          },
                      },
                  })
        }
    }

    const handleVesselStatusDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            date: date,
        })
    }

    const handleVesselStatusReturnDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            expectedReturn: date,
        })
    }

    const [createVesselStatus] = useMutation(CREATE_VESSELSTATUS, {
        onCompleted: (response: any) => {
            const data = response.createVesselStatus
            setVesselStatus({
                ...vesselStatus,
                vesselID: data?.vesselID,
                date: data?.date,
                status: data?.status,
                comment: data?.comment,
                reason: data?.reason,
                otherReason: data?.otherReason,
                expectedReturn: data?.expectedReturn,
            })
            setDisplayEditStatus(false)
        },
        onError: (error: any) => {
            toast({
                title: 'Error',
                description: error.message,
                variant: 'destructive',
            })
        },
    })

    const handleVesselStatusChange = (value: any) => {
        setVesselStatus({
            ...vesselStatus,
            status: value?.value,
        })
    }

    const handleVesselStatusReasonChange = (value: any) => {
        setVesselStatus({
            ...vesselStatus,
            reason: value?.value,
        })
    }

    return (
        <div>
            <div>
                <H3>
                    <span className="font-medium">Task:</span>{' '}
                    {maintenanceChecks?.name
                        ? ' ' + maintenanceChecks.name
                        : ' Task #' + taskId}
                </H3>
                {!isEmpty(completedRecurringTasks) && (
                    <div className="flex items-center">
                        {taskTab !== 'task' && (
                            <Button
                                title="Completed Tasks"
                                className="hover:"
                                iconLeft="check"
                                onClick={() => {
                                    setTaskTab('completed')
                                }}
                            />
                        )}
                        {taskTab === 'completed' && (
                            <Button
                                title="Task Details"
                                className="hover:"
                                iconLeft="back_arrow"
                                onClick={() => {
                                    setTaskTab('task')
                                }}
                            />
                        )}
                    </div>
                )}
            </div>
            <Separator className="my-4" />
            {taskTab === 'task' && (
                <div className="space-y-4">
                    {vesselStatus && (
                        <Card className="w-full">
                            <CardContent className="flex items-start gap-2 p-0">
                                <Checkbox
                                    id="vesselStatus-onChangeComplete"
                                    checked={
                                        vesselStatus?.status === 'OutOfService'
                                    }
                                    onCheckedChange={(checked) => {
                                        handleSetVesselStatus(checked === true)
                                    }}
                                    disabled={
                                        vesselStatus?.status === 'OnVoyage'
                                    }
                                />
                                <div
                                    className={`${vesselStatus?.status === 'OnVoyage' ? 'opacity-60' : ''}`}>
                                    <Label
                                        htmlFor="vesselStatus-onChangeComplete"
                                        className="cursor-pointer uppercase font-medium"
                                        label={`Make vessel out of service${
                                            vesselStatus?.status
                                                ? ' - ' + vesselStatus.status
                                                : ''
                                        }`}>
                                        <div
                                            className={`text-sm ${vesselStatus?.status === 'OnVoyage' ? 'text-red-vivid-600' : ''}`}>
                                            {vesselStatus?.status === 'OnVoyage'
                                                ? "This vessel is on voyage and can't set it out of service"
                                                : 'Until this task is complete the related vessel cannot be used normally'}
                                        </div>
                                    </Label>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                    <Label
                        className="my-4 w-full"
                        htmlFor="task-name"
                        label="Title">
                        <Input
                            id="task-name"
                            defaultValue={maintenanceChecks?.name}
                            type="text"
                            placeholder="Task name"
                        />
                    </Label>

                    <div className="flex flex-col sm:flex-row w-full gap-4">
                        {inSidebar ? (
                            <Combobox
                                label="Vessel"
                                id="vessel-combobox"
                                options={
                                    vessels && vessels.length > 0
                                        ? vessels.map((vessel: any) => ({
                                              value: vessel.id,
                                              label: vessel.title,
                                          }))
                                        : []
                                }
                                buttonClassName="w-full"
                                labelClassName="w-full"
                                isLoading={!vessels || vessels.length === 0}
                                defaultValues={
                                    currentVessel
                                        ? {
                                              value: currentVessel.id,
                                              label: currentVessel.title,
                                          }
                                        : undefined
                                }
                                onChange={handleOnChangeVessel}
                                placeholder="Select vessel"
                            />
                        ) : (
                            <Combobox
                                id="vessel-combobox"
                                label="Vessel"
                                isLoading={!vessels || vessels.length === 0}
                                options={
                                    vessels && vessels.length > 0
                                        ? vessels.map((vessel: any) => ({
                                              value: vessel.id,
                                              label: vessel.title,
                                          }))
                                        : []
                                }
                                buttonClassName="w-full"
                                labelClassName="w-full"
                                defaultValues={
                                    maintenanceChecks?.basicComponent?.id > 0
                                        ? {
                                              label: maintenanceChecks
                                                  .basicComponent.title,
                                              value: maintenanceChecks
                                                  .basicComponent.id,
                                          }
                                        : maintenanceChecks?.inventory?.vessel
                                                ?.id
                                          ? {
                                                label: maintenanceChecks
                                                    ?.inventory.vessel?.title,
                                                value: maintenanceChecks
                                                    ?.inventory.vessel.id,
                                            }
                                          : undefined
                                }
                                onChange={handleOnChangeVessel}
                                placeholder="Select vessel"
                            />
                        )}
                        <Combobox
                            label="Inventory"
                            isLoading={!inventories || inventories.length < 0}
                            id="task-inventory"
                            buttonClassName="w-full"
                            labelClassName="w-full"
                            options={
                                inventories && inventories.length > 0
                                    ? filterInventories(inventories).map(
                                          (inventory: any) => ({
                                              value: inventory.id,
                                              label: inventory.item,
                                          }),
                                      )
                                    : []
                            }
                            value={inventoryDefaultValue}
                            onChange={handleOnChangeInventory}
                            placeholder="Select inventory item"
                        />
                    </div>

                    <Combobox
                        id="task-category"
                        options={[
                            {
                                label: ' --- Add new category --- ',
                                value: 'NEW_CATEGORY',
                            },
                            ...(categoryList && categoryList.length > 0
                                ? categoryList
                                : []),
                        ]}
                        isLoading={!categoryList || categoryList.length === 0}
                        label="Group to:"
                        defaultValues={
                            categoryList &&
                            categoryList.length > 0 &&
                            categoryList.filter(
                                (option: any) =>
                                    option.value === currentTask?.category,
                            ).length > 0
                                ? categoryList.filter(
                                      (option: any) =>
                                          option.value ===
                                          currentTask?.category,
                                  )[0]
                                : categoryList &&
                                    categoryList.length > 0 &&
                                    categoryList.filter(
                                        (option: any) =>
                                            option.value ===
                                            maintenanceChecks
                                                ?.maintenanceCategory?.id,
                                    )?.length > 0
                                  ? categoryList.filter(
                                        (option: any) =>
                                            option.value ===
                                            maintenanceChecks
                                                ?.maintenanceCategory?.id,
                                    )[0]
                                  : undefined
                        }
                        placeholder="Select Category"
                        onChange={handleChangeCategpory}
                    />

                    <div className="mb-6 space-y-4">
                        <DatePicker
                            mode="single"
                            label="Due date"
                            type="date"
                            disabled={isDateDisabled()}
                            value={expiryDate}
                            onChange={(newDate) => {
                                if (
                                    maintenanceChecks?.maintenanceSchedule
                                        ?.occursEveryType === 'Hours'
                                ) {
                                    toast({
                                        title: 'Error',
                                        description:
                                            'This task has recurring based on engine hours and not allow the date edit',
                                        variant: 'destructive',
                                    })
                                } else {
                                    handleExpiryChange(newDate)
                                }
                            }}
                            placeholder="Select due date"
                            clearable={true}
                        />

                        {(currentTask?.occursEveryType === 'Hours' ||
                            currentTask?.occursEveryType === 'Uses') && (
                            <P className=" mt-2 text-destructive">
                                This task has a recurring period based on the
                                number of hours/uses
                            </P>
                        )}
                        <Label
                            htmlFor="task-recurring"
                            position="right"
                            className="cursor-pointer font-medium uppercase w-fit"
                            label="This task is recurring">
                            <Checkbox
                                id="task-recurring"
                                checked={displayRecurringTasks}
                                onCheckedChange={(checked) => {
                                    handleDisplayRecurringTasks({
                                        target: {
                                            checked: checked === true,
                                        },
                                    })
                                }}
                            />
                        </Label>
                    </div>
                    {/*<div className="my-4">
                        <Input
                            id={`task-name`}
                            defaultValue={maintenanceChecks?.name}
                            type="text"
                            className={''}
                            placeholder="Task name"
                        />
                    </div>*/}

                    <div className="w-full pb-6">
                        {isDesktop ? (
                            <Tabs
                                value={activeTab}
                                onValueChange={setActiveTab}>
                                <ScrollArea className="w-full pb-2">
                                    <TabsList className="inline-flex w-max px-1">
                                        <TabsTrigger value="Details">
                                            Details
                                        </TabsTrigger>
                                        <TabsTrigger value="Sub-tasks">
                                            Sub-tasks
                                        </TabsTrigger>
                                        <TabsTrigger value="Links-docs">
                                            Links-docs
                                        </TabsTrigger>
                                        {displayRecurringTasks && (
                                            <TabsTrigger value="Recurring schedule">
                                                Recurring schedule
                                            </TabsTrigger>
                                        )}
                                        <TabsTrigger value="Notes & updates">
                                            Notes & updates
                                        </TabsTrigger>
                                    </TabsList>
                                </ScrollArea>

                                {/* Tab Content */}
                                <TabsContent value="Details">
                                    <div className="space-y-4">
                                        <Label
                                            htmlFor="task-description"
                                            label="Task Description">
                                            {maintenanceChecks &&
                                                maintenanceChecks.id && (
                                                    <Editor
                                                        id="task-description"
                                                        placeholder="Task description"
                                                        className={''}
                                                        content={content}
                                                        handleEditorChange={
                                                            handleEditorChange
                                                        }
                                                    />
                                                )}
                                        </Label>
                                        <UploadCloudFlare
                                            files={attachments}
                                            setFiles={setAttachments}
                                        />
                                        <Label
                                            label="Reference"
                                            htmlFor="task-workorder">
                                            <Input
                                                id={`task-workorder`}
                                                defaultValue={
                                                    maintenanceChecks?.workOrderNumber
                                                }
                                                type="text"
                                                placeholder="Work order/Reference"
                                            />
                                        </Label>
                                        <Combobox
                                            label="Allocated to:"
                                            options={
                                                crewMembers
                                                    ? crewMembers.map(
                                                          (member: any) => ({
                                                              value: member.id,
                                                              label: `${member.firstName ?? ''} ${member.surname ?? ''}`,
                                                          }),
                                                      )
                                                    : []
                                            }
                                            isLoading={
                                                !crewMembers ||
                                                crewMembers.length === 0
                                            }
                                            defaultValues={
                                                maintenanceChecks?.assignedTo
                                                    ? {
                                                          label: `${maintenanceChecks.assignedTo.firstName ?? ''} ${maintenanceChecks.assignedTo.surname ?? ''}`,
                                                          value: maintenanceChecks
                                                              .assignedTo.id,
                                                      }
                                                    : null
                                            }
                                            onChange={(value: any) =>
                                                setCurrentTask({
                                                    ...currentTask,
                                                    assignees: value.value,
                                                })
                                            }
                                            placeholder="Select team"
                                        />
                                        <P>
                                            An email will be sent to the
                                            allocated team with order reference
                                            (if any) and details of this task.
                                        </P>

                                        <Combobox
                                            id="task-priority"
                                            label="Priority"
                                            options={priorityOptions}
                                            isLoading={!inventories}
                                            defaultValues={
                                                priorityOptions
                                                    .filter(
                                                        (option: any) =>
                                                            option.value ===
                                                            maintenanceChecks?.severity,
                                                    )
                                                    .map((option: any) => ({
                                                        value: option.value,
                                                        label: option.label,
                                                    }))[0]
                                            }
                                            placeholder="Select priority"
                                            onChange={(value: any) =>
                                                setCurrentTask({
                                                    ...currentTask,
                                                    severity: value.value,
                                                })
                                            }
                                        />
                                    </div>
                                </TabsContent>

                                <TabsContent value="Sub-tasks">
                                    {subTasks.length > 0 && (
                                        <Card className="w-full">
                                            <CardHeader className="pb-3">
                                                <CardTitle>Subtasks</CardTitle>
                                                <div className="flex items-center gap-4">
                                                    <Progress
                                                        value={subtaskProgress}
                                                        className="h-2 mt-2"
                                                    />
                                                    <span className="leading-none flex items-center">
                                                        {subtaskProgress}
                                                    </span>
                                                </div>
                                            </CardHeader>
                                            <CardContent>
                                                <ScrollArea className="max-h-[400px] pr-4">
                                                    <div className="space-y-3">
                                                        {subTasks.map(
                                                            (subtask: any) => {
                                                                const inventory =
                                                                    inventories?.find(
                                                                        (
                                                                            i: any,
                                                                        ) =>
                                                                            i.id ===
                                                                            subtask
                                                                                .maintenanceScheduleSubTask
                                                                                .inventoryID,
                                                                    )

                                                                return (
                                                                    <div
                                                                        key={`${subtask.id}-subtask`}
                                                                        className={`flex items-start justify-between p-3 rounded-lg border ${
                                                                            subtask.status ===
                                                                            'Completed'
                                                                                ? 'bg-muted/50'
                                                                                : 'bg-background'
                                                                        }`}>
                                                                        <div className="flex items-start gap-3 flex-1">
                                                                            <Checkbox
                                                                                id={
                                                                                    subtask.id
                                                                                }
                                                                                checked={
                                                                                    subtask.status ===
                                                                                    'Completed'
                                                                                }
                                                                                onCheckedChange={(
                                                                                    checked,
                                                                                ) => {
                                                                                    handleUpdateSubTask(
                                                                                        {
                                                                                            target: {
                                                                                                id: subtask.id,
                                                                                                checked:
                                                                                                    checked ===
                                                                                                    true,
                                                                                            },
                                                                                        },
                                                                                    )
                                                                                }}
                                                                                className="mt-0.5"
                                                                            />
                                                                            <div className="space-y-1 flex-1">
                                                                                <Label
                                                                                    htmlFor={
                                                                                        subtask.id
                                                                                    }
                                                                                    className={`block font-medium cursor-pointer ${
                                                                                        subtask.status ===
                                                                                        'Completed'
                                                                                            ? 'line-through text-muted-foreground'
                                                                                            : ''
                                                                                    }`}>
                                                                                    {
                                                                                        subtask
                                                                                            .maintenanceScheduleSubTask
                                                                                            .task
                                                                                    }
                                                                                </Label>

                                                                                {inventory && (
                                                                                    <Badge
                                                                                        variant="outline"
                                                                                        className="font-normal">
                                                                                        {
                                                                                            inventory.item
                                                                                        }
                                                                                    </Badge>
                                                                                )}

                                                                                {subtask.findings && (
                                                                                    <Dialog>
                                                                                        <TooltipProvider>
                                                                                            <Tooltip>
                                                                                                <TooltipTrigger
                                                                                                    asChild>
                                                                                                    <DialogTrigger
                                                                                                        asChild>
                                                                                                        <Button
                                                                                                            variant="outline"
                                                                                                            size="sm"
                                                                                                            className="mt-1 h-7 gap-1.5 text-xs">
                                                                                                            <AlertCircle className="h-3.5 w-3.5" />
                                                                                                            View
                                                                                                            Findings
                                                                                                        </Button>
                                                                                                    </DialogTrigger>
                                                                                                </TooltipTrigger>
                                                                                                <TooltipContent>
                                                                                                    <p>
                                                                                                        View
                                                                                                        detailed
                                                                                                        findings
                                                                                                    </p>
                                                                                                </TooltipContent>
                                                                                            </Tooltip>
                                                                                        </TooltipProvider>

                                                                                        <DialogContent>
                                                                                            <DialogHeader>
                                                                                                <DialogTitle>
                                                                                                    Task
                                                                                                    Findings
                                                                                                </DialogTitle>
                                                                                                <DialogDescription>
                                                                                                    Details
                                                                                                    for{' '}
                                                                                                    {
                                                                                                        subtask
                                                                                                            .maintenanceScheduleSubTask
                                                                                                            .task
                                                                                                    }
                                                                                                </DialogDescription>
                                                                                            </DialogHeader>
                                                                                            <div className="mt-2 p-4 bg-muted/50 rounded-lg">
                                                                                                {
                                                                                                    subtask.findings
                                                                                                }
                                                                                            </div>
                                                                                        </DialogContent>
                                                                                    </Dialog>
                                                                                )}
                                                                            </div>
                                                                        </div>

                                                                        {(subtask
                                                                            ?.completedBy
                                                                            ?.firstName ||
                                                                            subtask?.dateCompleted) && (
                                                                            <div className="text-sm text-muted-foreground flex items-center gap-2 ml-2">
                                                                                {subtask.status ===
                                                                                    'Completed' && (
                                                                                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                                                                                )}
                                                                                <div className="text-right">
                                                                                    {subtask
                                                                                        ?.completedBy
                                                                                        ?.firstName && (
                                                                                        <div>
                                                                                            {
                                                                                                subtask
                                                                                                    .completedBy
                                                                                                    .firstName
                                                                                            }{' '}
                                                                                            {
                                                                                                subtask
                                                                                                    .completedBy
                                                                                                    .surname
                                                                                            }
                                                                                        </div>
                                                                                    )}
                                                                                    {subtask?.dateCompleted && (
                                                                                        <div>
                                                                                            {formatDate(
                                                                                                subtask.dateCompleted,
                                                                                            )}
                                                                                        </div>
                                                                                    )}
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                )
                                                            },
                                                        )}
                                                    </div>
                                                </ScrollArea>
                                            </CardContent>
                                        </Card>
                                    )}
                                    <div className="my-4 flex justify-end">
                                        <Button
                                            variant="outline"
                                            iconLeft={Plus}
                                            onClick={() => {
                                                setOpenSubTaskDialog(true)
                                                setSubtaskContent('')
                                            }}>
                                            Add Sub-task
                                        </Button>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        ) : (
                            <div className="space-y-4">
                                <div className="flex flex-col space-y-2">
                                    <Button
                                        variant={
                                            activeTab === 'Details'
                                                ? 'primary'
                                                : 'outline'
                                        }
                                        onClick={() => setActiveTab('Details')}
                                        className="justify-start">
                                        Details
                                    </Button>
                                    <Button
                                        variant={
                                            activeTab === 'Sub-tasks'
                                                ? 'primary'
                                                : 'outline'
                                        }
                                        onClick={() =>
                                            setActiveTab('Sub-tasks')
                                        }
                                        className="justify-start">
                                        Sub-tasks
                                    </Button>
                                    <Button
                                        variant={
                                            activeTab === 'Links-docs'
                                                ? 'primary'
                                                : 'outline'
                                        }
                                        onClick={() =>
                                            setActiveTab('Links-docs')
                                        }
                                        className="justify-start">
                                        Links-docs
                                    </Button>
                                    {displayRecurringTasks && (
                                        <Button
                                            variant={
                                                activeTab ===
                                                'Recurring schedule'
                                                    ? 'primary'
                                                    : 'outline'
                                            }
                                            onClick={() =>
                                                setActiveTab(
                                                    'Recurring schedule',
                                                )
                                            }
                                            className="justify-start">
                                            Recurring schedule
                                        </Button>
                                    )}
                                    <Button
                                        variant={
                                            activeTab === 'Notes & updates'
                                                ? 'primary'
                                                : 'outline'
                                        }
                                        onClick={() =>
                                            setActiveTab('Notes & updates')
                                        }
                                        className="justify-start">
                                        Notes & updates
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>

                    {activeTab === 'Links-docs' && (
                        <>
                            <Label label="Links">
                                {vessels && (
                                    <Input
                                        id="task-title"
                                        type="text"
                                        className={''}
                                        placeholder="Type a link and press Enter"
                                        onKeyDown={async (
                                            event: React.KeyboardEvent<HTMLInputElement>,
                                        ) => {
                                            if (event.key === 'Enter') {
                                                const inputValue = (
                                                    event.target as HTMLInputElement
                                                ).value
                                                await createSeaLogsFileLinks({
                                                    variables: {
                                                        input: {
                                                            link: inputValue,
                                                        },
                                                    },
                                                })
                                                ;(
                                                    event.target as HTMLInputElement
                                                ).value = '' // Clear input value
                                            }
                                        }}
                                    />
                                )}
                            </Label>
                            <div className="my-4 flex">
                                {linkSelectedOption
                                    ? linkSelectedOption.map((link: any) => (
                                          <div key={link.value}>
                                              {linkItem(link)}
                                          </div>
                                      ))
                                    : fileLinks.map((link: any) => (
                                          <div key={link.value}>
                                              {linkItem(link)}
                                          </div>
                                      ))}
                            </div>
                            <Separator className="my-4 w-full" />
                            <div>
                                <Label label="Documents">
                                    <FileUpload
                                        setDocuments={setDocuments}
                                        text=""
                                        subText="Drag files here or upload"
                                        documents={documents}
                                    />
                                </Label>
                                <div className="my-4">
                                    {documents.length > 0 && (
                                        <ListBox
                                            aria-label="Documents"
                                            className={``}>
                                            {documents.map((document: any) => (
                                                <ListBoxItem
                                                    key={document.id}
                                                    textValue={document.name}
                                                    className="flex items-center gap-8 justify-between p-2.5  rounded-lg border    mb-4 hover:0 hover-">
                                                    <FileItem
                                                        document={document}
                                                    />
                                                    <Button
                                                        className="flex gap-2 items-center"
                                                        onClick={() =>
                                                            deleteFile(
                                                                document.id,
                                                            )
                                                        }>
                                                        <XCircleIcon className="w-5 h-5 alert cursor-pointer" />
                                                    </Button>
                                                </ListBoxItem>
                                            ))}
                                        </ListBox>
                                    )}
                                </div>
                            </div>
                        </>
                    )}
                    {displayRecurringTasks && (
                        <>
                            {!edit_recurring_task ? (
                                <Loading errorMessage="Oops You do not have the permission to view this section." />
                            ) : (
                                <>
                                    {activeTab === 'Recurring schedule' && (
                                        <>
                                            <Label label="Schedule details" />
                                            {/*<div className="my-4">
                                                    <Input
                                                        id="task-title"
                                                        type="text"
                                                        defaultValue={
                                                            recurringTasks
                                                                ? recurringTasks.title
                                                                : ''
                                                        }
                                                        className={''}
                                                        placeholder="Title"
                                                    />
                                                </div>*/}
                                            <Label
                                                htmlFor="task-frequency"
                                                label="Occurs every">
                                                <Input
                                                    id="task-frequency"
                                                    defaultValue={
                                                        recurringTasks
                                                            ? recurringTasks.occursEvery
                                                            : '1'
                                                    }
                                                    type="number"
                                                    placeholder="Schedule every"
                                                    min={1}
                                                    onChange={(e) => {
                                                        setCurrentMaintenanceCheck(
                                                            {
                                                                ...currentMaintenanceCheck,
                                                                maintenanceSchedule:
                                                                    {
                                                                        ...currentMaintenanceCheck.maintenanceSchedule,
                                                                        occursEvery:
                                                                            e
                                                                                .target
                                                                                .value ??
                                                                            '1',
                                                                    },
                                                            },
                                                        )
                                                        setScheduleEvery(
                                                            e.target.value,
                                                        )
                                                        updateDueDate()
                                                    }}
                                                />
                                            </Label>
                                            <Combobox
                                                id="task-recurring-type"
                                                label="Occurrence type"
                                                options={recurringType}
                                                value={
                                                    currentTask?.occursEveryType
                                                        ? recurringType
                                                              .filter(
                                                                  (
                                                                      option: any,
                                                                  ) =>
                                                                      option.value ===
                                                                      currentTask.occursEveryType,
                                                              )
                                                              .map(
                                                                  (
                                                                      option: any,
                                                                  ) => ({
                                                                      value: option.value,
                                                                      label: option.label,
                                                                  }),
                                                              )[0]
                                                        : recurringType
                                                              .filter(
                                                                  (
                                                                      option: any,
                                                                  ) =>
                                                                      option.value ===
                                                                      recurringTasks.occursEveryType,
                                                              )
                                                              .map(
                                                                  (
                                                                      option: any,
                                                                  ) => ({
                                                                      value: option.value,
                                                                      label: option.label,
                                                                  }),
                                                              )[0]
                                                }
                                                placeholder="Select type"
                                                onChange={(value: any) => {
                                                    setCurrentMaintenanceCheck({
                                                        ...currentMaintenanceCheck,
                                                        maintenanceSchedule: {
                                                            ...currentMaintenanceCheck.maintenanceSchedule,
                                                            occursEveryType:
                                                                value.value,
                                                        },
                                                    })
                                                    handleSetOccursEveryType(
                                                        value.value,
                                                    )
                                                }}
                                            />
                                            {taskIsDateType() && (
                                                <DatePicker
                                                    mode="single"
                                                    type="date"
                                                    label="Last completed date"
                                                    value={
                                                        startDate
                                                            ? startDate.toDate()
                                                            : lastScheduleDate().toDate()
                                                    }
                                                    onChange={
                                                        handleStartDateChange
                                                    }
                                                    placeholder="Enter last schedule date"
                                                    disabled={
                                                        !edit_recurring_task
                                                    }
                                                />
                                            )}
                                            {taskIsEngineHourType() && (
                                                <>
                                                    {engineList.length > 0 &&
                                                        engineList.map(
                                                            (engine: any) => (
                                                                <div className="my-4">
                                                                    <div className="flex w-full gap-4 items-center">
                                                                        <div className=" w-1/2 ">
                                                                            <Label
                                                                                htmlFor={`check_engine-${engine.id}`}
                                                                                data-ripple="true"
                                                                                data-ripple-color="dark"
                                                                                data-ripple-dark="true"
                                                                                label={`${engine.title}${engine.type ? ' - ' + engine.type : ''}`}>
                                                                                <Input
                                                                                    type="checkbox"
                                                                                    id={`check_engine-${engine.id}`}
                                                                                    onChange={(
                                                                                        e,
                                                                                    ) =>
                                                                                        handleCheckEngineCheck(
                                                                                            e,
                                                                                            engine.id,
                                                                                        )
                                                                                    }
                                                                                    checked={
                                                                                        displayCheckEngineCheck[
                                                                                            engine
                                                                                                .id
                                                                                        ]
                                                                                            ? displayCheckEngineCheck[
                                                                                                  engine
                                                                                                      .id
                                                                                              ]
                                                                                                  .value
                                                                                            : recurringTasks?.engineUsage?.nodes?.find(
                                                                                                  (
                                                                                                      engineUsage: any,
                                                                                                  ) =>
                                                                                                      engineUsage
                                                                                                          .engine
                                                                                                          .id ===
                                                                                                      engine.id,
                                                                                              )
                                                                                                  ?.isScheduled
                                                                                    }
                                                                                />
                                                                                <span className="absolute transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"></span>
                                                                                <span className="ml-3">
                                                                                    <div>
                                                                                        <span className="">
                                                                                            Engine
                                                                                            Hours:{' '}
                                                                                        </span>
                                                                                        {
                                                                                            engine.currentHours
                                                                                        }
                                                                                    </div>
                                                                                </span>
                                                                            </Label>
                                                                        </div>
                                                                        <div className="w-full">
                                                                            <Input
                                                                                id={`check_engine_hours-${engine.id}`}
                                                                                name="check_engine_hours"
                                                                                type="number"
                                                                                min={
                                                                                    0
                                                                                }
                                                                                placeholder="Enter last schedule hours"
                                                                                onChange={(
                                                                                    e,
                                                                                ) =>
                                                                                    setEngineHours(
                                                                                        {
                                                                                            ...engineHours,
                                                                                            [engine.id]:
                                                                                                e
                                                                                                    .target
                                                                                                    .value,
                                                                                        },
                                                                                    )
                                                                                }
                                                                                onBlur={(
                                                                                    e,
                                                                                ) =>
                                                                                    handleEngineHours(
                                                                                        e,
                                                                                        engine.id,
                                                                                    )
                                                                                }
                                                                                value={
                                                                                    engineHours[
                                                                                        engine
                                                                                            .id
                                                                                    ]
                                                                                        ? engineHours[
                                                                                              engine
                                                                                                  .id
                                                                                          ]
                                                                                        : recurringTasks?.engineUsage?.nodes?.find(
                                                                                              (
                                                                                                  engineUsage: any,
                                                                                              ) =>
                                                                                                  engineUsage
                                                                                                      .engine
                                                                                                      .id ===
                                                                                                  engine.id,
                                                                                          )
                                                                                              ?.lastScheduleHours
                                                                                }
                                                                                className={
                                                                                    ''
                                                                                }
                                                                                aria-describedby="expiry-last-schedule-error"
                                                                                required
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            ),
                                                        )}
                                                </>
                                            )}
                                            <Textarea
                                                id="recurring-task-description"
                                                rows={4}
                                                defaultValue={
                                                    recurringTasks
                                                        ? recurringTasks.description
                                                        : ''
                                                }
                                                placeholder="Description and notes"
                                            />
                                            <Label
                                                className="relative flex items-center pr-3 rounded-full cursor-pointer"
                                                htmlFor="task-reminders"
                                                data-ripple="true"
                                                data-ripple-color="dark"
                                                data-ripple-dark="true"
                                                label="CREATE YOUR OWN REMINDERS (Optional)">
                                                <Input
                                                    type="checkbox"
                                                    id="task-reminders"
                                                    className="before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border 0 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:opacity-0 before:transition-opacity      hover:before:opacity-10"
                                                    onChange={
                                                        handleDisplayWarnings
                                                    }
                                                    checked={displayWarnings}
                                                />
                                            </Label>
                                            {displayWarnings && (
                                                <>
                                                    <Separator className="my-6" />

                                                    <Label
                                                        label="Low warning within (days)"
                                                        htmlFor="low-warn-within">
                                                        <Input
                                                            type="number"
                                                            id="low-warn-within"
                                                            placeholder="Low warning within"
                                                            disabled
                                                            value={
                                                                (currentTask.occursEveryType
                                                                    ? currentTask.occursEveryType
                                                                    : recurringTasks.occursEveryType) ===
                                                                'Months'
                                                                    ? '14'
                                                                    : (
                                                                            currentTask.occursEveryType
                                                                                ? currentTask.occursEveryType
                                                                                : recurringTasks.occursEveryType
                                                                        )
                                                                      ? '7'
                                                                      : '0'
                                                            }
                                                        />
                                                    </Label>

                                                    <Label
                                                        label="Medium warning within (days)"
                                                        htmlFor="medium-warn-within">
                                                        <Input
                                                            type="number"
                                                            id="medium-warn-within"
                                                            placeholder="Medium warning within"
                                                            disabled
                                                            value={
                                                                (currentTask.occursEveryType
                                                                    ? currentTask.occursEveryType
                                                                    : recurringTasks.occursEveryType) ===
                                                                'Months'
                                                                    ? '7'
                                                                    : (
                                                                            currentTask.occursEveryType
                                                                                ? currentTask.occursEveryType
                                                                                : recurringTasks.occursEveryType
                                                                        )
                                                                      ? '3'
                                                                      : '0'
                                                            }
                                                        />
                                                    </Label>

                                                    <Label
                                                        label="High warning within (days)"
                                                        htmlFor="high-warn-within">
                                                        <Input
                                                            type="number"
                                                            id="high-warn-within"
                                                            placeholder="High warning within"
                                                            disabled
                                                            value={
                                                                (currentTask.occursEveryType
                                                                    ? currentTask.occursEveryType
                                                                    : recurringTasks.occursEveryType) ===
                                                                'Months'
                                                                    ? '3'
                                                                    : (
                                                                            currentTask.occursEveryType
                                                                                ? currentTask.occursEveryType
                                                                                : recurringTasks.occursEveryType
                                                                        )
                                                                      ? '1'
                                                                      : '0'
                                                            }
                                                        />
                                                    </Label>
                                                </>
                                            )}
                                        </>
                                    )}
                                </>
                            )}
                        </>
                    )}
                    {activeTab === 'Task costs' && (
                        <>
                            <p className="   max-w-[40rem] leading-loose">
                                Track the expected and actual costs of repairs
                                and maintenance in SeaLogs reports module.
                            </p>
                            <div className="my-4 flex flex-col w-full">
                                <Label
                                    htmlFor="task-projected"
                                    label="Projected costs">
                                    <Input
                                        id="task-projected"
                                        defaultValue={
                                            maintenanceChecks?.projected
                                        }
                                        type="number"
                                        placeholder="Projected"
                                        onChange={updateCostsDifference}
                                    />
                                </Label>
                            </div>
                            <div className="my-4 flex flex-col w-full">
                                <Label
                                    htmlFor="task-actual"
                                    label="Actual costs">
                                    <Input
                                        id="task-actual"
                                        defaultValue={maintenanceChecks?.actual}
                                        type="number"
                                        placeholder="Actual"
                                        onChange={updateCostsDifference}
                                    />
                                </Label>
                            </div>
                            <div className="my-4 flex flex-col w-full">
                                <Label
                                    htmlFor="task-difference"
                                    label="Difference">
                                    <div
                                        id="task-difference"
                                        className="w-full p-2 border-t border-dashed">
                                        {costsDifference}
                                    </div>
                                </Label>
                            </div>
                        </>
                    )}
                    {activeTab === 'Notes & updates' && (
                        <>
                            {taskRecords.length > 0 && (
                                <ListBox aria-label="Task Records">
                                    {taskRecords.map((record: any) => (
                                        <ListBoxItem
                                            key={`${record.id}-record`}
                                            textValue={record.description}
                                            className="border  rounded-md flex items-start justify-between mb-4 p-4">
                                            <Label
                                                className="relative inline-flex items-center pr-3 rounded-full cursor-pointer"
                                                htmlFor={record.id}
                                                data-ripple="true"
                                                data-ripple-color="dark"
                                                data-ripple-dark="true"
                                                label="">
                                                <div className="flex-grow overflow-scroll ql-container">
                                                    <div className="ql-editor !px-0">
                                                        <div
                                                            dangerouslySetInnerHTML={{
                                                                __html: record.description,
                                                            }}></div>
                                                    </div>
                                                </div>
                                            </Label>
                                            <div className="flex gap-2 mt-5">
                                                <Button
                                                    iconLeft={<Pencil />}
                                                    variant="ghost"
                                                    iconOnly
                                                    className="w-6 h-6 -mt-2 ml-0.5"
                                                    onClick={() => {
                                                        setReviewContent(
                                                            record.description,
                                                        )
                                                        setCommentData(record)
                                                        setOpenRecordsDialog(
                                                            true,
                                                        )
                                                        setCommentTime(
                                                            record.time,
                                                        )
                                                    }}
                                                />
                                                <Button
                                                    iconLeft={<Trash />}
                                                    variant="ghost"
                                                    iconOnly
                                                    className="w-6 h-6 -mt-2 ml-0.5"
                                                    onClick={() => {
                                                        setDeleteRecordID(
                                                            record.id,
                                                        )
                                                        setOpenDeleteRecordDialog(
                                                            true,
                                                        )
                                                    }}
                                                />
                                            </div>
                                        </ListBoxItem>
                                    ))}
                                </ListBox>
                            )}
                            <div className="mt-2 flex justify-end">
                                <Button
                                    variant="outline"
                                    iconLeft={Plus}
                                    onClick={() => {
                                        setReviewContent('')
                                        setCommentData(false)
                                        setOpenRecordsDialog(true)
                                        setCommentTime(false)
                                    }}>
                                    Add note / update
                                </Button>
                            </div>
                        </>
                    )}
                    <Separator className="my-6" />
                    <div className="pb-6 flex flex-col sm:flex-row gap-4">
                        <Combobox
                            id="task-status"
                            label="Task status"
                            options={statusOptions || []}
                            isLoading={
                                !inventories ||
                                !statusOptions ||
                                statusOptions.length === 0
                            }
                            defaultValues={
                                statusOptions && maintenanceChecks?.status
                                    ? statusOptions
                                          .filter(
                                              (option: any) =>
                                                  option.value ===
                                                  maintenanceChecks?.status.replaceAll(
                                                      '_',
                                                      ' ',
                                                  ),
                                          )
                                          .map((option: any) => ({
                                              value: option.value,
                                              label: option.label,
                                          }))[0]
                                    : undefined
                            }
                            placeholder="Select status"
                            onChange={(value: any) => {
                                if (
                                    !complete_task &&
                                    value.value === 'Completed'
                                ) {
                                    toast({
                                        title: 'Error',
                                        description:
                                            'You do not have the permission to complete this task.',
                                        variant: 'destructive',
                                    })
                                    return
                                }
                                setCurrentTask({
                                    ...currentTask,
                                    status: value.value.replaceAll('_', ' '),
                                })
                            }}
                            labelClassName="w-full"
                        />
                        {currentTask?.status === 'Completed' && (
                            <DatePicker
                                mode="single"
                                type="date"
                                label="Completion date"
                                value={
                                    completionDate
                                        ? completionDate instanceof Date
                                            ? completionDate
                                            : typeof completionDate.toDate ===
                                                'function'
                                              ? completionDate.toDate()
                                              : new Date(
                                                    completionDate.toString(),
                                                )
                                        : undefined
                                }
                                className="w-full"
                                onChange={handleCompletionChange}
                                placeholder="Enter completion date"
                                disabled={!complete_task}
                                clearable={true}
                            />
                        )}
                        {currentTask?.status === 'Completed' && (
                            <Combobox
                                id="task-completed-by"
                                label="Completed by"
                                labelClassName="w-full"
                                options={
                                    crewMembers
                                        ? crewMembers?.map((member: any) => ({
                                              value: member.id,
                                              label: `${member.firstName ?? ''} ${member.surname ?? ''}`,
                                          }))
                                        : []
                                }
                                isLoading={!crewMembers || !maintenanceChecks}
                                defaultValues={
                                    maintenanceChecks?.completedBy
                                        ? {
                                              label: `${maintenanceChecks.completedBy.firstName ?? ''} ${maintenanceChecks.completedBy.surname ?? ''}`,
                                              value: maintenanceChecks
                                                  .completedBy.id,
                                          }
                                        : null
                                }
                                onChange={(value: any) =>
                                    (currentTask.completedBy = value.value)
                                }
                                placeholder="Select team"
                            />
                        )}
                    </div>
                </div>
            )}

            {/*<div className="my-4">
                    <Label>
                        Signature
                    </Label>

                    <div className="w-full md:w-96">
                        <SignaturePad
                            signature={signature}
                            onSignatureChanged={onSignatureChanged}
                        />
                    </div>

                </div>*/}

            {taskTab === 'completed' && (
                <>
                    <TableWrapper headings={[' ', 'Assigned To', 'Due date']}>
                        {completedRecurringTasks.map(
                            (maintenanceCheck: any) => (
                                <tr
                                    key={maintenanceCheck.id}
                                    className={` ${maintenanceCheck.id} group border-b hover:`}>
                                    <td className="px-2 py-3 lg:px-6 min-w-1/2">
                                        <div className="flex items-center justify-between">
                                            <span className=" text-foreground flex items-center">
                                                <Link
                                                    href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                                    className="focus:outline-none">
                                                    {maintenanceCheck.name}
                                                </Link>
                                                <div
                                                    className={`inline-block rounded px-3 py-1 ml-3  ${maintenanceCheck?.isOverDue?.status === 'High' ? 'text-destructive bg-slred-100' : ''} ${maintenanceCheck?.isOverDue?.status === 'Low' || maintenanceCheck?.isOverDue?.status === 'Upcoming' || maintenanceCheck?.isOverDue?.status === 'Completed' ? 'text-slgreen-1000 bg-slneon-100' : ''} ${maintenanceCheck?.isOverDue?.status === 'Medium' || maintenanceCheck?.isOverDue?.days === 'Save As Draft' ? 'text-yellow-600 bg-yellow-100' : ''} `}>
                                                    {maintenanceCheck?.isOverDue
                                                        ?.status &&
                                                        [
                                                            'High',
                                                            'Medium',
                                                            'Low',
                                                        ].includes(
                                                            maintenanceCheck
                                                                .isOverDue
                                                                .status,
                                                        ) &&
                                                        maintenanceCheck
                                                            ?.isOverDue?.days}
                                                    {maintenanceCheck?.isOverDue
                                                        ?.status ===
                                                        'Completed' &&
                                                        maintenanceCheck
                                                            ?.isOverDue
                                                            ?.days ===
                                                            'Save As Draft' &&
                                                        maintenanceCheck
                                                            ?.isOverDue?.days}
                                                    {maintenanceCheck?.isOverDue
                                                        ?.status ===
                                                        'Upcoming' &&
                                                        maintenanceCheck
                                                            ?.isOverDue?.days}
                                                    {maintenanceCheck?.isOverDue
                                                        ?.status ===
                                                        'Completed' &&
                                                        isEmpty(
                                                            maintenanceCheck
                                                                ?.isOverDue
                                                                ?.days,
                                                        ) &&
                                                        maintenanceCheck
                                                            ?.isOverDue?.status}
                                                    {maintenanceCheck?.isOverDue
                                                        ?.status ===
                                                        'Completed' &&
                                                        !isEmpty(
                                                            maintenanceCheck
                                                                ?.isOverDue
                                                                ?.days,
                                                        ) &&
                                                        maintenanceCheck
                                                            ?.isOverDue
                                                            ?.days !==
                                                            'Save As Draft' &&
                                                        maintenanceCheck
                                                            ?.isOverDue?.days}
                                                    {/* {maintenanceCheck?.isOverDue?.status !==
                                        'Completed' &&
                                        maintenanceCheck?.isOverDue?.status !==
                                            'Upcoming' &&
                                        maintenanceCheck?.isOverDue?.days} */}
                                                </div>
                                            </span>
                                            <div className="flex">
                                                {maintenanceCheck.basicComponentID !==
                                                    null &&
                                                    vessels
                                                        ?.filter(
                                                            (vessel: any) =>
                                                                vessel?.id ==
                                                                maintenanceCheck.basicComponentID,
                                                        )
                                                        .map((vessel: any) => (
                                                            <div
                                                                key={vessel.id}
                                                                className="inline-block rounded px-3 py-1 ml-3   ">
                                                                <span>
                                                                    {
                                                                        vessel.title
                                                                    }
                                                                </span>
                                                            </div>
                                                        ))}
                                                <div className="w-14 flex items-center pl-1">
                                                    {maintenanceCheck.comments !==
                                                        null && (
                                                        <DialogTrigger>
                                                            <Button className=" outline-none px-1">
                                                                <ChatBubbleBottomCenterTextIcon className="w-5 h-5 text-slgreen-1000" />
                                                            </Button>
                                                            <Popover>
                                                                <PopoverWrapper>
                                                                    {
                                                                        maintenanceCheck.comments
                                                                    }
                                                                </PopoverWrapper>
                                                            </Popover>
                                                        </DialogTrigger>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            {maintenanceCheck.description}
                                        </div>
                                        <div></div>
                                    </td>
                                    <td className="px-2 py-3  ">
                                        {crewInfo &&
                                            crewInfo
                                                .filter(
                                                    (crew: any) =>
                                                        crew.id ===
                                                        maintenanceCheck.assignedToID,
                                                )
                                                .map(
                                                    (
                                                        crew: any,
                                                        index: number,
                                                    ) => {
                                                        return (
                                                            <Link
                                                                key={index}
                                                                href={`/crew/info?id=${crew.id}`}
                                                                className="focus:outline-none group-hover:">
                                                                {crew.firstName}{' '}
                                                                {crew.surname}
                                                            </Link>
                                                        )
                                                    },
                                                )}
                                    </td>
                                    <td className="px-2 py-3  ">
                                        {formatDate(maintenanceCheck.expires)}
                                        <div
                                            className={`inline-block rounded px-3 py-1 ml-3  ${maintenanceCheck.status == 'Completed' ? 'bg-slneon-100 text-slgreen-1000' : 'bg-slred-100 text-destructive'}`}>
                                            <span>
                                                {maintenanceCheck.status.replaceAll(
                                                    '_',
                                                    ' ',
                                                )}
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            ),
                        )}
                    </TableWrapper>
                </>
            )}
            {/* Using the ActionFooter component for consistent footer styling */}
            {inSidebar ? (
                <div className="flex justify-end gap-3 mt-4">
                    {isDesktop && (
                        <Button
                            variant="back"
                            iconLeft={<ArrowLeft />}
                            onClick={handleCancel}>
                            Cancel
                        </Button>
                    )}
                    <Button
                        variant="destructive"
                        iconLeft={<Trash />}
                        onClick={() => {
                            if (
                                !delete_task ||
                                (displayRecurringTasks && !edit_recurring_task)
                            ) {
                                toast({
                                    title: 'Error',
                                    description:
                                        'You do not have permission to delete this task',
                                    variant: 'destructive',
                                })
                                return
                            }
                            setOpenDeleteTaskDialog(true)
                        }}>
                        {isDesktop ? 'Archive task' : 'Delete'}
                    </Button>
                    <Button
                        variant="primary"
                        iconLeft={<Check />}
                        disabled={saveDisabled}
                        onClick={handleUpdate}>
                        {isDesktop ? 'Update task' : 'Update'}
                    </Button>
                </div>
            ) : (
                <ActionFooter
                    showFooter={true}
                    showCreateTask={false}
                    saveText={isDesktop ? 'Update task' : 'Update'}
                    saveIcon="check"
                    saveDisabled={saveDisabled}
                    onCancel={isDesktop ? undefined : handleCancel}
                    onSave={handleUpdateTask}
                    noBorder={true}
                    className="gap-3">
                    <Button
                        variant="destructive"
                        iconLeft={<Trash />}
                        onClick={() => {
                            if (
                                !delete_task ||
                                (displayRecurringTasks && !edit_recurring_task)
                            ) {
                                toast({
                                    title: 'Error',
                                    description:
                                        'You do not have permission to delete this task',
                                    variant: 'destructive',
                                })
                                return
                            }
                            setOpenDeleteTaskDialog(true)
                        }}>
                        {isDesktop ? 'Archive task' : 'Delete'}
                    </Button>
                </ActionFooter>
            )}
            <AlertDialogNew
                openDialog={openSubTaskDialog}
                setOpenDialog={setOpenSubTaskDialog}
                handleCreate={handleCreateSubTask}
                size="xl"
                actionText="Create SubTask"
                title="Create new sub-task">
                <AlertDialogBody className="space-y-4">
                    <Input
                        id={`subtask-name`}
                        type="text"
                        className={''}
                        placeholder="Sub-task"
                    />

                    <Combobox
                        id="subtask-inventory"
                        label="Inventory item (optional)"
                        options={
                            inventories && inventories.length > 0
                                ? filterInventories(inventories)
                                      ?.filter(
                                          (i: any) =>
                                              i.vesselID ==
                                                  maintenanceChecks
                                                      ?.basicComponent?.id ||
                                              i.vesselID ==
                                                  currentTask?.basicComponentID,
                                      )
                                      ?.map((inventory: any) => ({
                                          value: inventory.id,
                                          label: inventory.item,
                                      }))
                                : []
                        }
                        isLoading={!inventories || inventories.length === 0}
                        onChange={handleSubtaskOnChangeInventory}
                        placeholder="Select inventory item"
                    />

                    <Editor
                        id="comment"
                        placeholder="Comment"
                        className={''}
                        content={subtaskContent}
                        handleEditorChange={handleSubtaskEditorChange}
                    />
                </AlertDialogBody>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={displayUpdateSubTask}
                setOpenDialog={setDisplayUpdateSubTask}
                title="Sub-task details"
                handleCancel={() => setDisplayUpdateSubTask(false)}
                handleCreate={() => handleUpdateSubTask('updateSubTask')}
                handleDestructiveAction={() =>
                    handleUpdateSubTask('deleteSubTask')
                }
                showDestructiveAction={true}
                actionText="Update"
                destructiveActionText="Delete"
                cancelText="Cancel"
                size="lg">
                <Label label="Sub-task name" htmlFor="subtask-name">
                    <Input
                        id="subtask-name"
                        type="text"
                        defaultValue={
                            subTasks.filter(
                                (subtask: any) =>
                                    subtask.id === currentSubTaskCheckID,
                            )[0]?.maintenanceScheduleSubTask.task
                        }
                        onChange={(e: any) =>
                            setSubTask(
                                subTasks.map((subtask: any) =>
                                    subtask.id === currentSubTaskCheckID
                                        ? {
                                              ...subtask,
                                              maintenanceScheduleSubTask: {
                                                  ...subtask.maintenanceScheduleSubTask,
                                                  task: e.target.value,
                                              },
                                          }
                                        : subtask,
                                ),
                            )
                        }
                        placeholder={`Sub-task ${currentSubTaskCheckID}`}
                    />
                </Label>

                <div className="my-4">
                    <Combobox
                        id="subtask-inventory"
                        label="Inventory item (optional)"
                        options={
                            inventories && inventories.length > 0
                                ? filterInventories(inventories)
                                      ?.filter(
                                          (i: any) =>
                                              i.vesselID ==
                                                  maintenanceChecks
                                                      ?.basicComponent?.id ||
                                              i.vesselID ==
                                                  currentTask?.basicComponentID,
                                      )
                                      ?.map((inventory: any) => ({
                                          value: inventory.id,
                                          label: inventory.item,
                                      }))
                                : []
                        }
                        isLoading={!inventories || inventories.length === 0}
                        value={
                            inventories?.filter(
                                (i: any) => i.id == subtaskInventoryValue,
                            )?.length > 0
                                ? {
                                      value: inventories.find(
                                          (i: any) =>
                                              i.id == subtaskInventoryValue,
                                      ).id,
                                      label: inventories.find(
                                          (i: any) =>
                                              i.id == subtaskInventoryValue,
                                      ).item,
                                  }
                                : undefined
                        }
                        onChange={handleSubtaskOnChangeInventory}
                        placeholder="Select inventory item"
                    />
                </div>

                {taskRecords &&
                    taskRecords.length > 0 &&
                    taskRecords.filter(
                        (record: any) =>
                            record.subTaskID == currentSubTaskCheckID,
                    ).length > 0 && (
                        <ScrollArea className="mb-4 max-h-[300px]">
                            {taskRecords
                                .filter(
                                    (record: any) =>
                                        record.subTaskID ==
                                        currentSubTaskCheckID,
                                )
                                .map((record: any) => (
                                    <div
                                        key={`${record.id}-record`}
                                        className="flex items-start justify-between p-3 border rounded-md mb-2">
                                        <div className="flex-grow overflow-auto">
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: record.description,
                                                }}
                                            />
                                        </div>
                                        <div className="flex gap-2 ml-2 flex-shrink-0">
                                            <Button
                                                iconLeft={<Pencil />}
                                                variant="ghost"
                                                iconOnly
                                                onClick={() => {
                                                    setReviewContent(
                                                        record.description,
                                                    )
                                                    setCommentData(record)
                                                    setOpenRecordsDialog(true)
                                                }}
                                            />
                                            <Button
                                                iconLeft={<Trash />}
                                                variant="ghost"
                                                iconOnly
                                                onClick={() => {
                                                    setDeleteRecordID(record.id)
                                                    setOpenDeleteRecordDialog(
                                                        true,
                                                    )
                                                }}
                                            />
                                        </div>
                                    </div>
                                ))}
                        </ScrollArea>
                    )}

                <Button
                    variant="text"
                    iconLeft={<Plus />}
                    onClick={() => {
                        setReviewContent('')
                        setCommentData(false)
                        setOpenRecordsDialog(true)
                    }}>
                    Add Record
                </Button>

                <UploadCloudFlare
                    files={subTaskAttachments}
                    setFiles={setSubTaskAttachments}
                />

                <Label label="Comment" htmlFor="comment">
                    <Textarea
                        id="comment"
                        placeholder="Comment"
                        rows={8}
                        value={subtaskContent}
                        onChange={(e) =>
                            handleSubtaskEditorChange(e.target.value)
                        }
                    />
                </Label>

                <Separator className="my-6" />

                <Label
                    position="right"
                    interactive
                    htmlFor="task-alertChange"
                    label={alertSubTaskStatus ? 'Completed' : 'Complete'}>
                    <Checkbox
                        id="task-alertChange"
                        checked={alertSubTaskStatus}
                        onCheckedChange={() =>
                            setAlertSubTaskStatus(!alertSubTaskStatus)
                        }
                    />
                </Label>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={displayAddFindings}
                setOpenDialog={setDisplayAddFindings}
                handleCreate={() => handleUpdateSubTask('updateFindings')}
                actionText="Complete SubTask"
                title="Add Findings">
                <AlertDialogBody>
                    <Textarea
                        id={`subtask-findings`}
                        rows={4}
                        className={'w-full'}
                        placeholder="Findings"
                        defaultValue={
                            subTasks.filter(
                                (subtask: any) =>
                                    subtask.id === currentSubTaskCheckID,
                            )[0]?.findings
                        }
                    />

                    <Combobox
                        id="comment-author"
                        label="Crew member"
                        options={members || []}
                        isLoading={!members || members.length === 0}
                        placeholder="Crew member"
                        className={'w-full'}
                        value={
                            members?.find(
                                (member: any) => member.value == authorID,
                            ) || undefined
                        }
                        onChange={(value: any) => setAuthorID(value?.value)}
                    />

                    <DatePicker
                        mode="single"
                        type="datetime"
                        label="Select completed date"
                        value={scheduleCompletedDate}
                        onChange={handleScheduleCompletedDateChange}
                        clearable={true}
                        confirmSelection={true}
                    />
                    <>
                        {/* <DialogTrigger>
                                <Button
                                    className={`w-full`}
                                    isDisabled={
                                        currentTask?.occursEveryType ===
                                            'Hours' ||
                                        currentTask?.occursEveryType === 'Uses'
                                    }>
                                    <Input
                                        id="completed-date"
                                        name="completed-date"
                                        type="text"
                                        readOnly
                                        placeholder="Select completed date"
                                        value={
                                            scheduleCompletedDate
                                                ? scheduleCompletedDate
                                                : ''
                                        }
                                        className={''}
                                        aria-describedby="completed-date-error"
                                        required
                                    />
                                </Button>
                                <ModalOverlay
                                    className={({ isEntering, isExiting }) => `
                                        fixed inset-0 z-[15001] overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
                                        ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
                                        ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}
                                        `}>
                                    <Modal>
                                        <Dialog
                                            role="alertdialog"
                                            className="outline-none relative">
                                            {({ close }) => (
                                                <LocalizationProvider
                                                    dateAdapter={AdapterDayjs}>
                                                    <StaticDateTimePicker
                                                        className={`p-0 mr-4`}
                                                        defaultValue={dayjs()}
                                                        onAccept={
                                                            handleScheduleCompletedDateChange
                                                        }
                                                        onClose={close}
                                                    />
                                                </LocalizationProvider>
                                            )}
                                        </Dialog>
                                    </Modal>
                                </ModalOverlay>
                            </DialogTrigger> */}
                    </>
                </AlertDialogBody>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openDeleteTaskDialog}
                setOpenDialog={setOpenDeleteTaskDialog}
                handleCreate={handleDeleteCheck}
                actionText="Archive Task"
                title="Archive Task"
                variant="danger"
                showDestructiveAction={true}
                handleDestructiveAction={handleDeleteCheck}>
                <P>Are you sure you want to Archive this task?</P>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openRecordsDialog}
                setOpenDialog={setOpenRecordsDialog}
                handleCreate={handleSaveRecords}
                title={commentData?.id > 0 ? 'Update note' : 'Create new note'}
                actionText={commentData?.id > 0 ? 'Update' : 'Create Record'}>
                <AlertDialogBody className="space-y-4">
                    <DatePicker
                        mode="single"
                        type="datetime"
                        label="Time of completion"
                        value={commentTime}
                        onChange={handleCommentTimeChange}
                        clearable={true}
                        confirmSelection={true}
                    />

                    <Combobox
                        id="comment-author"
                        label="Crew member"
                        options={members || []}
                        isLoading={!members}
                        buttonClassName={'w-full'}
                        placeholder="Crew member"
                        value={
                            members?.find(
                                (member: any) =>
                                    member.value == commentData?.author?.id,
                            ) || undefined
                        }
                        onChange={(value: any) =>
                            setCommentData({
                                ...commentData,
                                authorID: value?.value,
                            })
                        }
                    />

                    <Label label="Comment" htmlFor="comment">
                        <Textarea
                            id="comment"
                            placeholder="Comment"
                            rows={8}
                            value={reviewContent}
                            onChange={(e) =>
                                handleReviewEditorChange(e.target.value)
                            }
                        />
                    </Label>
                </AlertDialogBody>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openDeleteRecordDialog}
                setOpenDialog={setOpenDeleteRecordDialog}
                title="Delete Record"
                variant="warning"
                showDestructiveAction={true}
                handleDestructiveAction={handleDeleteRecord}>
                <P>Are you sure you want to delete this record?</P>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={createCategoryDialog}
                setOpenDialog={setCreateCategoryDialog}
                handleCreate={handleCreateCategory}
                actionText="Create Category"
                title="Create New Category">
                <Input
                    id={`task-new-category`}
                    name={`task-new-category`}
                    type="text"
                    className={'w-full'}
                    placeholder="Category"
                />
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={displayEditStatus}
                setOpenDialog={setDisplayEditStatus}
                handleCreate={handleUpdateVesselStatus}
                actionText="Update"
                title="Update Vessel Status">
                <AlertDialogBody className="space-y-4">
                    <DatePicker
                        mode="single"
                        type="date"
                        label="Date"
                        value={
                            vesselStatus?.date
                                ? dayjs(vesselStatus.date).toDate()
                                : undefined
                        }
                        onChange={handleVesselStatusDate}
                        placeholder="Select date"
                        clearable={true}
                    />
                    <Combobox
                        id="vessel-status"
                        options={
                            vesselStatuses && vesselStatuses.length > 0
                                ? vesselStatuses
                                : []
                        }
                        placeholder="Status"
                        isLoading={
                            !vesselStatuses || vesselStatuses.length === 0
                        }
                        value={
                            vesselStatuses && vesselStatuses.length > 0
                                ? vesselStatuses.find(
                                      (status: any) =>
                                          vesselStatus?.status === status.value,
                                  ) || undefined
                                : undefined
                        }
                        onChange={handleVesselStatusChange}
                    />
                    {vesselStatus?.status === 'OutOfService' && (
                        <Combobox
                            id="vessel-status-reason"
                            options={
                                vesselStatusReason &&
                                vesselStatusReason.length > 0
                                    ? vesselStatusReason
                                    : []
                            }
                            placeholder="Reason"
                            isLoading={
                                !vesselStatusReason ||
                                vesselStatusReason.length === 0
                            }
                            value={
                                vesselStatusReason &&
                                vesselStatusReason.length > 0
                                    ? vesselStatusReason.find(
                                          (status: any) =>
                                              vesselStatus?.reason ===
                                              status.value,
                                      ) || undefined
                                    : undefined
                            }
                            onChange={handleVesselStatusReasonChange}
                        />
                    )}
                    {vesselStatus?.status === 'OutOfService' &&
                        vesselStatus?.reason === 'Other' && (
                            <Textarea
                                id="vessel-status-other"
                                className="w-full"
                                placeholder="Other description"
                                value={vesselStatus?.otherReason}
                                onChange={(e) =>
                                    setVesselStatus({
                                        ...vesselStatus,
                                        otherReason: e.target.value,
                                    })
                                }
                            />
                        )}
                    {vesselStatus?.status === 'OutOfService' && (
                        <Label label="Comment" htmlFor="vessel-status-comment">
                            <Textarea
                                id="vessel-status-comment"
                                placeholder="Comment"
                                rows={6}
                                value={vesselStatus?.comment || ''}
                                onChange={(e) =>
                                    setVesselStatus({
                                        ...vesselStatus,
                                        comment: e.target.value,
                                    })
                                }
                            />
                        </Label>
                    )}
                    {vesselStatus?.status === 'OutOfService' && (
                        <DatePicker
                            mode="single"
                            type="date"
                            label="Expected return date"
                            value={
                                vesselStatus?.expectedReturn
                                    ? (() => {
                                          const date = dayjs(
                                              vesselStatus.expectedReturn,
                                          )
                                          return typeof date.toDate ===
                                              'function'
                                              ? date.toDate()
                                              : new Date(date.toString())
                                      })()
                                    : undefined
                            }
                            onChange={handleVesselStatusReturnDate}
                            placeholder="Expected return date"
                            clearable={true}
                        />
                    )}
                </AlertDialogBody>
            </AlertDialogNew>
        </div>
    )
}
